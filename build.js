#!/usr/bin/env node
/**
 * TempVoice Discord Bot - Build Script
 * This script builds the TypeScript code and provides diagnostic information
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Starting build process for TempVoice Discord Bot...');

// Check if tsconfig.json exists
const tsconfigPath = path.join(__dirname, 'tsconfig.json');
if (!fs.existsSync(tsconfigPath)) {
  console.error('Error: tsconfig.json not found!');
  process.exit(1);
}

// Log tsconfig content
console.log('\ntsconfig.json contents:');
const tsconfig = require(tsconfigPath);
console.log(JSON.stringify(tsconfig, null, 2));

// Clean dist directory
const distDir = path.join(__dirname, 'dist');
if (fs.existsSync(distDir)) {
  console.log('\nCleaning dist directory...');
  try {
    // On Windows, we need to use rimraf or similar for recursive delete
    // As a simple alternative, we'll just log the existing files
    console.log('Existing files in dist directory:');
    listFilesRecursive(distDir);
  } catch (error) {
    console.error('Error accessing dist directory:', error);
  }
}

// Run the TypeScript compiler with verbose output
console.log('\nRunning TypeScript compiler...');
try {
  execSync('npx tsc --listFiles', { stdio: 'inherit' });
  console.log('Build completed successfully.');
} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}

// Check output
console.log('\nChecking build output...');
if (!fs.existsSync(distDir)) {
  console.error('Error: dist directory was not created by the build!');
  process.exit(1);
}

// List the generated files
console.log('\nGenerated files in dist directory:');
listFilesRecursive(distDir);

// Check for specific important files
const importantFiles = [
  'index.js',
  'shard.js',
];

console.log('\nVerifying important files:');
let missingFiles = false;
for (const file of importantFiles) {
  const filePath = path.join(distDir, file);
  if (fs.existsSync(filePath)) {
    console.log(`✓ ${file} exists`);
  } else {
    console.error(`✗ ${file} is missing!`);
    missingFiles = true;
  }
}

if (missingFiles) {
  console.error('\nSome important files are missing. Build may be incomplete.');
  process.exit(1);
}

console.log('\nBuild verification successful!');

// Helper function to list files recursively
function listFilesRecursive(dir, basePath = '') {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    const relativePath = path.join(basePath, file);
    
    if (stat.isDirectory()) {
      console.log(`📁 ${relativePath}/`);
      listFilesRecursive(filePath, relativePath);
    } else {
      console.log(`📄 ${relativePath} (${formatBytes(stat.size)})`);
    }
  });
}

// Format bytes to human-readable
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
} 