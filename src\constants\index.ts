/**
 * Constants for <PERSON><PERSON> Bot
 * Centralizes all constant values used throughout the app
 */

// Discord API error codes - used to handle specific error responses from the Discord API
export const ERROR_CODES = {
  UNKNOWN_ACCOUNT: 10001,
  UNKNOWN_APPLICATION: 10002,
  UNKNOWN_CHANNEL: 10003,
  UNKNOWN_GUILD: 10004,
  UNKNOWN_INTEGRATION: 10005,
  UNKNOWN_INVITE: 10006,
  UNKNOWN_MEMBER: 10007,
  UNKNOWN_MESSAGE: 10008,
  UNKNOWN_PERMISSION_OVERWRITE: 10009,
  UNKNOWN_PROVIDER: 10010,
  UNKNOWN_ROLE: 10011,
  UNKNOWN_TOKEN: 10012,
  UNKNOWN_USER: 10013,
  UNKNOWN_EMOJI: 10014,
  UNKNOWN_WEBHOOK: 10015,
  UNKNOWN_WEBHOOK_SERVICE: 10016,
  UNKNOWN_SESSION: 10020,
  UNKNOWN_BAN: 10026,
  UNKNOWN_SKU: 10027,
  UNKNOWN_STORE_LISTING: 10028,
  UNK<PERSON>OW<PERSON>_ENTITLEMENT: 10029,
  UNKNOWN_BUILD: 10030,
  UNKNOWN_LOBBY: 10031,
  UNKNOWN_BRANCH: 10032,
  UNKNOWN_STORE_DIRECTORY_LAYOUT: 10033,
  UNKNOWN_REDISTRIBUTABLE: 10036,
  UNKNOWN_GIFT_CODE: 10038,
  UNKNOWN_STREAM: 10049,
  UNKNOWN_PREMIUM_SERVER_SUBSCRIBE_COOLDOWN: 10050,
  UNKNOWN_GUILD_TEMPLATE: 10057,
  UNKNOWN_DISCOVERABLE_SERVER_CATEGORY: 10059,
  UNKNOWN_STICKER: 10060,
  UNKNOWN_INTERACTION: 10062,
  UNKNOWN_APPLICATION_COMMAND: 10063,
  UNKNOWN_APPLICATION_COMMAND_PERMISSIONS: 10066,
  UNKNOWN_STAGE_INSTANCE: 10067,
  UNKNOWN_GUILD_MEMBER_VERIFICATION_FORM: 10068,
  UNKNOWN_GUILD_WELCOME_SCREEN: 10069,
  UNKNOWN_GUILD_SCHEDULED_EVENT: 10070,
  UNKNOWN_GUILD_SCHEDULED_EVENT_USER: 10071,
  UNKNOWN_TAG: 10087,
};

/**
 * Cooldown tiers in seconds
 * Defines standard cooldown durations for different severity levels
 */
export const COOLDOWN_TIERS = {
  LIGHT: 2, // 2 seconds - For lightweight, frequently used commands
  MODERATE: 5, // 5 seconds - For standard commands with moderate impact
  HEAVY: 10, // 10 seconds - For commands with significant server impact
  CRITICAL: 30, // 30 seconds - For commands with major changes to server structure
};

/**
 * Command cooldowns by action
 * Maps specific actions to their appropriate cooldown tiers
 */
export const ACTIONS_COOLDOWN: Record<string, number> = {
  // Slash commands
  setup: COOLDOWN_TIERS.CRITICAL,
  help: COOLDOWN_TIERS.LIGHT,
  ping: COOLDOWN_TIERS.LIGHT,

  // Voice channel operations
  'channel:create': COOLDOWN_TIERS.HEAVY,
  'channel:delete': COOLDOWN_TIERS.MODERATE,
  'channel:rename': COOLDOWN_TIERS.MODERATE,
  'channel:limit': COOLDOWN_TIERS.MODERATE,
  'channel:privacy': COOLDOWN_TIERS.MODERATE,
};

/**
 * Global rate limit configuration
 * Prevents API abuse by limiting overall request frequency
 */
export const GLOBAL_RATE_LIMIT = {
  PER_SECOND: 45, // 45 requests per second max to comply with Discord's rate limits
  WINDOW: 1000, // 1 second window for rate limit calculation
};

/**
 * System maintenance intervals in milliseconds
 * Defines how frequently different maintenance tasks should run
 */
export const INTERVALS = {
  SAVE_DATA: 2 * 60 * 1000, // 2 minutes - How often to save temporary channel data (increased for memory optimization)
  CLEANUP: 30 * 1000, // 30 seconds - How often to check for and clean up orphaned channels (increased for memory optimization)
  CATEGORY_CLEANUP: 45 * 1000, // 45 seconds - How often to check MyVC categories for empty channels (increased for memory optimization)
  STATS: 30 * 60 * 1000, // 30 minutes - How often to collect and log usage statistics (increased for memory optimization)
  PERFORMANCE_LOG: 2 * 60 * 60 * 1000, // 2 hours - How often to log performance metrics (increased for memory optimization)
  MEMORY_CHECK: 60 * 1000, // 1 minute - How often to check memory usage and trigger garbage collection if needed
};

/**
 * Anti-spam settings
 * Prevents rapid join/create actions that could strain the bot or server
 */
export const JOIN_COOLDOWN = 50; // 250ms between join events to prevent rapid-fire channel creation

export const SERVER_INVITE_LINK = 'https://discord.gg/F3sHNwThHb';
