import { Modal<PERSON>uilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } from 'discord.js';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'name';

export const execute = async (interaction, client, userChannel) => {
  try {
    const modal = new ModalBuilder()
      .setCustomId(`name_modal_${userChannel.id}`)
      .setTitle('Rename Channel');

    const input = new TextInputBuilder()
      .setCustomId('channel_name')
      .setLabel('New Channel Name')
      .setPlaceholder('Enter the new name')
      .setRequired(true)
      .setStyle(TextInputStyle.Short)

      .setMinLength(1)
      .setMaxLength(100);

    const actionRow: any = new ActionRowBuilder().addComponents(input);
    modal.addComponents(actionRow);

    await interaction.showModal(modal);
  } catch (error) {
    logger.error(`Error showing name modal: ${error.message}`);
    await handleInteractionError('name.execute', error, interaction, client);
  }
};

export const handleModalSubmit = async (interaction, client, targetChannel) => {
  const modalSource = `name.handleModalSubmit:modal:${interaction.customId}`;

  try {
    const newName = interaction.fields.getTextInputValue('channel_name');
    logger.debug(
      `${modalSource}: User ${interaction.user.tag} submitted new name "${newName}" for channel ${targetChannel.id}`
    );

    if (!newName || newName.length < 1 || newName.length > 100) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} Invalid channel name. Must be 1-100 characters.`,
      });
      return;
    }
    if (/^\s+$/.test(newName)) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} Channel name cannot be only spaces.`,
      });
      return;
    }

    await targetChannel.setName(newName, `Renamed by owner ${interaction.user.tag}`);
    logger.info(`Channel ${targetChannel.id} renamed to "${newName}" by ${interaction.user.tag}`);

    try {
      const userId = interaction.user.id;

      logger.debug(`Updated name preference for user ${userId}`);
    } catch (settingError) {
      logger.error(
        `Failed to save name preference for user ${interaction.user.id}: ${settingError}`
      );
    }

    await replyOrFollowUpEphemeral(interaction, {
      content: `${EMOJI[client.user.id].CHECK} Channel name updated to "${newName}".`,
    });
  } catch (error) {
    logger.error(`${modalSource}: Failed to handle name modal submission: ${error.message}`);
    await handleInteractionError(modalSource, error, interaction, client);
  }
};
