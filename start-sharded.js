#!/usr/bin/env node
/**
 * TempVoice Discord Bot - Sharded Start Script
 * This script starts the bot in sharded mode using the ShardingManager
 */

// Import required Node.js modules
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Set the environment variable to indicate we're in sharded mode
process.env.SHARDED = 'true';

console.log('Starting TempVoice Discord Bot in sharded mode...');

// Build the project to ensure we have the latest compiled code
console.log('Building project...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('Build completed successfully.');
} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}

// Check if the dist directory and main files exist after build
const distDir = path.join(__dirname, 'dist');
const srcDir = path.join(distDir, 'src');
const indexFile = path.join(srcDir, 'index.js');
const shardFile = path.join(srcDir, 'shard.js');

if (!fs.existsSync(distDir)) {
  console.error('Error: dist directory was not created by the build.');
  process.exit(1);
}

if (!fs.existsSync(srcDir)) {
  console.error('Error: dist/src directory was not created by the build.');
  process.exit(1);
}

if (!fs.existsSync(indexFile)) {
  console.error('Error: src/index.js was not compiled. Please check your tsconfig.json configuration.');
  console.error('Expected file path:', indexFile);
  process.exit(1);
}

if (!fs.existsSync(shardFile)) {
  console.error('Error: src/shard.js was not compiled. Please check your tsconfig.json configuration.');
  console.error('Expected file path:', shardFile);
  process.exit(1);
}

// Start the ShardingManager
try {
  console.log('Starting shard manager...');
  require('./dist/src/shard.js');
} catch (error) {
  console.error('Failed to start ShardingManager:', error);
  process.exit(1);
} 