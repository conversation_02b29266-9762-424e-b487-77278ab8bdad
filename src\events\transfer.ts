import {
  ActionRowBuilder,
  StringSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
} from 'discord.js';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'transfer';

export const execute = async (interaction, client, userChannel) => {
  try {
    const membersInChannel = userChannel.members
      .filter(member => member.id !== interaction.user.id)
      .map(member => ({ label: member.user.tag, value: member.id }));

    if (membersInChannel.length === 0) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} No other users are in your channel to transfer ownership to.`,
      });
      return;
    }

    const transferSelect = new StringSelectMenuBuilder()
      .setCustomId(`transfer_select_${userChannel.id}`)
      .setPlaceholder('Select a user to transfer ownership to')
      .setMinValues(1)
      .setMaxValues(1)
      .addOptions(membersInChannel);

    const row: any = new ActionRowBuilder().addComponents(transferSelect);

    await replyOrFollowUpEphemeral(interaction, {
      content:
        'Select a user currently **in this channel** to transfer ownership to. You will lose owner permissions.',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending transfer select menu: ${error.message}`);
    await handleInteractionError('transfer.execute', error, interaction, client);
  }
};

export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `transfer.handleSelectMenu:selectMenu:${interaction.customId}`;

  if (!interaction.isStringSelectMenu()) return;

  try {
    const selectedValue = interaction.values[0];
    const currentOwnerId = interaction.user.id;

    if (!selectedValue) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} No user selected.`,
        components: [],
      });
      return;
    }

    const targetUserId = selectedValue;

    const targetMember = await interaction.guild.members.fetch(targetUserId).catch(() => null);
    const selectedUser = targetMember?.user;

    if (!targetMember || !selectedUser) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} The selected user is no longer in this server.`,
        components: [],
      });
      return;
    }

    logger.debug(
      `${menuSource}: User ${currentOwnerId} selected ${selectedUser.tag} (${targetUserId}) for ownership transfer of channel ${targetChannel.id}`
    );

    if (targetMember.voice.channelId !== targetChannel.id) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} The selected user must be in the voice channel to receive ownership.`,
        components: [],
      });
      return;
    }

    const confirmButton = new ButtonBuilder()
      .setCustomId(`transfer_confirm_${targetChannel.id}_${targetUserId}`)
      .setLabel(`Transfer`)
      .setStyle(ButtonStyle.Primary)
      .setEmoji(EMOJI[client.user.id].CHECK);

    const cancelButton = new ButtonBuilder()
      .setCustomId(`cancel_${targetChannel.id}`)
      .setLabel('Cancel')
      .setStyle(ButtonStyle.Secondary)
      .setEmoji(EMOJI[client.user.id].CROSS);

    const row: any = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

    await replyOrFollowUpEphemeral(interaction, {
      content: `🚨 **Confirm Transfer**: Are you sure you want to transfer ownership of **${targetChannel.name}** to **${selectedUser.tag}**? You will lose owner privileges.`,
      components: [row],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle transfer select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};

/**
 * Handles actual transfer confirmation including updating ownership in client.tempChannels,
 * updating permissions, and applying the new owner's configuration from MongoDB
 *
 * @param {Interaction} interaction - The interaction from the button click
 * @param {Client} client - The Discord client
 * @param {string} channelId - The channel ID being transferred
 * @param {string} newOwnerId - The ID of the new owner
 */
export const handleTransferConfirmation = async (interaction, client, channelId, newOwnerId) => {
  const transferSource = `transfer.handleTransferConfirmation:button:${interaction.customId}`;

  try {
    const targetChannel = await interaction.guild?.channels.fetch(channelId).catch(() => null);
    if (!targetChannel) {
      logger.warn(`${transferSource}: Target channel ${channelId} not found.`);
      const errorEmbed = new EmbedBuilder()
        .setColor('#FF0000')
        .setTitle(`${EMOJI[client.user.id].CROSS} Channel Not Found`)
        .setDescription('The target channel no longer exists.')
        .setTimestamp();

      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
      return;
    }

    const oldOwnerId = client.tempChannels.get(channelId);
    if (!oldOwnerId) {
      logger.warn(`${transferSource}: Channel ${channelId} has no owner in tempChannels map.`);
      const errorEmbed = new EmbedBuilder()
        .setColor('#FF0000')
        .setTitle(`${EMOJI[client.user.id].CROSS} Transfer Error`)
        .setDescription('This channel is not properly registered in the system.')
        .setTimestamp();

      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
      return;
    }

    const newOwnerMember = await interaction.guild.members.fetch(newOwnerId).catch(() => null);
    if (!newOwnerMember || newOwnerMember.voice.channelId !== channelId) {
      logger.warn(`${transferSource}: New owner ${newOwnerId} is no longer in the channel.`);
      const errorEmbed = new EmbedBuilder()
        .setColor('#FF0000')
        .setTitle(`${EMOJI[client.user.id].CROSS} Transfer Failed`)
        .setDescription('The new owner must be in the voice channel to receive ownership.')
        .setTimestamp();

      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
      return;
    }

    client.tempChannels.set(channelId, newOwnerId);
    logger.info(
      `${transferSource}: Ownership of channel ${channelId} transferred from ${oldOwnerId} to ${newOwnerId}`
    );

    try {
      await client.db.updateChannelOwner(channelId, newOwnerId);
      logger.info(`${transferSource}: Updated channel owner in database for channel ${channelId}`);
    } catch (dbError) {
      logger.error(
        `${transferSource}: Failed to update database for channel ${channelId}: ${dbError.message}`
      );
    }

    try {
      logger.info(`${transferSource}: Resetting channel ${channelId} to default configuration`);

      const defaultName = `${newOwnerMember.user.username}'s channel`;
      await targetChannel.setName(defaultName);

      await targetChannel.setUserLimit(0);
      await targetChannel.setBitrate(64000);

      logger.info(`${transferSource}: Successfully reset channel configuration to defaults`);
    } catch (resetError) {
      logger.error(
        `${transferSource}: Failed to reset channel configuration: ${resetError.message}`
      );
    }

    let newOwnerConfig = null;
    try {
      newOwnerConfig = await client.db.getUserConfig(newOwnerId);
      logger.info(`${transferSource}: Retrieved config for new owner ${newOwnerId}`);
    } catch (configError) {
      logger.error(
        `${transferSource}: Failed to retrieve new owner config: ${configError.message}`
      );
    }

    try {
      await targetChannel.permissionOverwrites.edit(oldOwnerId, {
        ManageChannels: null,
        MuteMembers: null,
        MoveMembers: null,
      });

      await targetChannel.permissionOverwrites.edit(newOwnerId, {
        ManageChannels: true,
        MuteMembers: true,
        MoveMembers: true,
      });

      logger.info(`${transferSource}: Updated permissions for channel ${channelId}`);
    } catch (permError) {
      logger.error(`${transferSource}: Failed to update permissions: ${permError.message}`);
    }

    if (newOwnerConfig) {
      try {
        if (newOwnerConfig.channelNameFormat) {
          const formattedName = newOwnerConfig.channelNameFormat
            .replace('{username}', newOwnerMember.user.username)
            .replace('{tag}', newOwnerMember.user.tag);

          await targetChannel.setName(formattedName);
          logger.info(`${transferSource}: Updated channel name to ${formattedName}`);
        }

        if (newOwnerConfig.userLimit !== undefined) {
          await targetChannel.setUserLimit(newOwnerConfig.userLimit);
        }

        if (newOwnerConfig.bitrate !== undefined) {
          await targetChannel.setBitrate(newOwnerConfig.bitrate);
        }

        logger.info(`${transferSource}: Applied new owner's settings to channel ${channelId}`);
      } catch (settingsError) {
        logger.error(`${transferSource}: Failed to apply new settings: ${settingsError.message}`);
      }
    }

    const successEmbed = new EmbedBuilder()
      .setColor('#00FF00')
      .setTitle(`${EMOJI[client.user.id].CHECK} Ownership Transferred`)
      .setDescription(
        `Ownership of **${targetChannel.name}** has been transferred to <@${newOwnerId}>.`
      )
      .setTimestamp();

    await interaction.update({
      embeds: [successEmbed],
      components: [],
    });
  } catch (error) {
    logger.error(`${transferSource}: Unexpected error: ${error.message}`);

    const errorEmbed = new EmbedBuilder()
      .setColor('#FF0000')
      .setTitle(`${EMOJI[client.user.id].CROSS} Transfer Error`)
      .setDescription('An unexpected error occurred during ownership transfer.')
      .setTimestamp();

    if (!interaction.replied && !interaction.deferred) {
      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
    }
  }
};
