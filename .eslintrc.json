{"parser": "@typescript-eslint/parser", "extends": ["plugin:@typescript-eslint/recommended"], "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "plugins": ["@typescript-eslint", "unused-imports"], "rules": {"no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "off", "unused-imports/no-unused-imports": "warn", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}]}}