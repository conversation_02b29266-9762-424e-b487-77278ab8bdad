# Discord Bot Configuration
TOKEN=your_discord_bot_token_here
CLIENT_ID=your_client_id_here

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/tempvoice

# Logging Configuration
CONSOLE_LOG_LEVEL=INFO  # Options: ERROR, WARN, INFO, <PERSON><PERSON><PERSON>G

# Discord Webhook Configuration (optional)
# Leave empty to disable webhook logging
ERROR_WEBHOOK_URL=
GUILD_WEBHOOK_URL=
# Sharding Configuration
# For development mode, set SHARD_COUNT=1 and SHARDED=false
# For production with start-sharded.js, set SHARD_COUNT=auto and SHARDED=true
SHARD_COUNT=1
SHARDED=false

# API Configuration (if implemented)
API_KEY=your_api_key_here

# Development Mode Settings
NODE_ENV=development  # Options: development, production
