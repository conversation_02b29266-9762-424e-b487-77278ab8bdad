# Use the official Node.js 18 runtime as the base image
FROM node:18-slim

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json (if available)
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy the rest of the application code
COPY . .

# Build the TypeScript code
RUN npm run build

# Create a non-root user to run the application
RUN groupadd -r botuser && useradd -r -g botuser botuser
RUN chown -R botuser:botuser /app
USER botuser

# Expose the port that the app runs on
EXPOSE 8080

# Set environment variables for Google Cloud Run
ENV NODE_ENV=production
ENV PORT=8080

# Command to run the application
CMD ["npm", "run", "start:gcp"]
