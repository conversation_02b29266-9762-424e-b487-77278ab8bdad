/**
 * Helper utilities for MyVC Bot
 */
import { EmbedBuilder } from 'discord.js';
import logger from '../utils/logger';
import { EMOJI } from '../constants/emoji';

/**
 * Format uptime from milliseconds to human-readable format
 * @param {number} ms - Uptime in milliseconds
 * @returns {string} Formatted uptime string
 */
function formatUptime(ms) {
  const seconds = Math.floor((ms / 1000) % 60);
  const minutes = Math.floor((ms / (1000 * 60)) % 60);
  const hours = Math.floor((ms / (1000 * 60 * 60)) % 24);
  const days = Math.floor(ms / (1000 * 60 * 60 * 24));

  return `${days}d ${hours}h ${minutes}m ${seconds}s`;
}

/**
 * Format bytes to human-readable format
 * @param {number} bytes - Bytes to format
 * @returns {string} Formatted string
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Create a standardized embed for responses
 * @param {string} message - Message content
 * @param {string} type - Type of message (success, error, info, warning)
 * @returns {EmbedBuilder} Formatted embed
 */
function createEmbed(message, type = 'info') {
  let color;
  switch (type.toLowerCase()) {
    case 'success':
      color = 0x2ecc71;
      break;
    case 'error':
      color = 0xe74c3c;
      break;
    case 'warning':
      color = 0xf1c40f;
      break;
    case 'info':
    default:
      color = 0x3498db;
      break;
  }

  return new EmbedBuilder().setColor(color).setDescription(message);
}

/**
 * Verify channel ownership
 * @param {string} channelId - The channel ID to check
 * @param {string} userId - The user ID to verify as owner
 * @param {Client} client - The Discord client
 * @returns {boolean} True if user is the owner, false otherwise
 */
function verifyChannelOwnership(channelId, userId, client) {
  if (!client.tempChannels.has(channelId)) {
    logger.info(`Channel ${channelId} not found in tempChannels map`);
    return false;
  }

  const ownerId = client.tempChannels.get(channelId);
  const isOwner = ownerId === userId;

  if (!isOwner) {
    logger.info(`User ${userId} attempted to modify channel ${channelId} owned by ${ownerId}`);
  }

  return isOwner;
}

/**
 * Safely reply to an interaction, handling different interaction states and potential errors
 * @param {Interaction} interaction - The Discord interaction to reply to
 * @param {Object} options - Reply options
 * @returns {Promise<Message|InteractionResponse|null>} The response or null if it failed
 */
function safeReply(interaction, client, options) {
  try {
    if (!interaction) {
      logger.debug('Cannot reply to interaction - interaction is null or undefined');
      return null;
    }

    if (interaction.replied) {
      logger.debug('Interaction already replied to, using editReply instead');
      try {
        return interaction.editReply(options);
      } catch (editError) {
        logger.error(`Failed to edit reply: ${editError.message}`);
        return null;
      }
    }

    if (interaction.deferred) {
      logger.debug('Interaction already deferred, using editReply instead');
      try {
        return interaction.editReply(options);
      } catch (editError) {
        logger.error(`Failed to edit reply after defer: ${editError.message}`);
        return null;
      }
    }

    if (!interaction.isRepliable()) {
      logger.debug('Cannot reply to interaction - not repliable');
      return null;
    }

    if (!options) options = {};

    if (options.content && !options.embeds && !options.content.includes('http')) {
      const emojiRegex =
        /^([\u{1F300}-\u{1F6FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{1F900}-\u{1F9FF}\u{1F1E0}-\u{1F1FF}❌⏰✅])\s*/u;
      const emojiMatch = options.content.match(emojiRegex);

      let description = options.content;

      if (emojiMatch) {
        description = options.content.replace(emojiRegex, '');
      }

      let color = 0x3498db;
      if (
        options.content.includes('failed') ||
        options.content.includes('error') ||
        options.content.includes(`${EMOJI[client.user.id].CROSS} `) ||
        options.content.includes('cannot') ||
        options.content.includes('wait')
      ) {
        color = 0xe74c3c;
      } else if (
        options.content.includes('success') ||
        options.content.includes(EMOJI[client.user.id].CHECK)
      ) {
        color = 0x2ecc71;
      } else if (options.content.includes('select') || options.content.includes('choose')) {
        color = 0xf1c40f;
      }

      const embed = {
        color: color,
        description: description,
      };

      const newOptions = {
        ...options,
        embeds: [embed],
        content: null,
      };

      options = newOptions;
    }

    if (options.ephemeral === true) {
      if (!options.flags) {
        options.flags = 64;
      }
      delete options.ephemeral;
    }

    try {
      if (options.defer === true) {
        delete options.defer;
        logger.debug('Deferring interaction reply');
        const result = interaction.deferReply({ ephemeral: options.flags === 64 });
        return result.then(() => interaction.editReply(options));
      }

      logger.debug('Sending immediate interaction reply');
      return interaction.reply(options);
    } catch (replyError) {
      if (replyError.code === 10062) {
        logger.debug('Interaction expired before reply could be sent');
        return null;
      }
      logger.error(`Error during interaction reply: ${replyError.message}`);
      throw replyError;
    }
  } catch (error) {
    logger.error(`Failed to handle interaction: ${error.message}`);
    if (error.code) {
      logger.error(`Discord error code: ${error.code}`);
    }
    return null;
  }
}

export { createEmbed, formatBytes, formatUptime, safeReply, verifyChannelOwnership };
