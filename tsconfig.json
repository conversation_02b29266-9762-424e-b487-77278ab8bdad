{"compilerOptions": {"target": "ES2020", "module": "commonjs", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": false, "skipLibCheck": true, "outDir": "dist", "baseUrl": ".", "rootDirs": ["src", "scripts"], "resolveJsonModule": true, "moduleResolution": "node", "noEmitOnError": false, "sourceMap": true}, "include": ["src/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist", "data", "logs"]}