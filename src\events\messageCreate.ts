import { EmbedBuilder, PermissionFlagsBits, Events, type Client, Message } from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { isDeveloper } from '../constants/devs';
import { EMOJI } from '../constants/emoji';

// Map to store prefixes per guild for quick access
const guildPrefixes = new Map();

export const name = Events.MessageCreate;

// Subscribe to prefix changes
dataManager.on('guildPrefixChanged', (guildId, newPrefix) => {
  guildPrefixes.set(guildId, newPrefix);
  logger.info(`Prefix for guild ${guildId} updated to ${newPrefix}`);
});

// Subscribe to guild settings changes
dataManager.on('guildSettingsChanged', (guildId, settings) => {
  if (settings.prefix) {
    guildPrefixes.set(guildId, settings.prefix);
    logger.info(`Prefix for guild ${guildId} updated to ${settings.prefix} via settings change`);
  }
});

export const execute = async (message: Message, client: Client) => {
  // Ignore bot messages to prevent potential loops
  if (message.author.bot) return;

  // Check if in a guild (for DM handling if needed)
  if (!message.guild) return;

  // Increment message counter
  if (global.incrementMessageCount) {
    global.incrementMessageCount();
  }

  try {
    // Check if server is blacklisted
    const blacklistInfo = await dataManager.isServerBlacklisted(message.guild.id);
    if (blacklistInfo) {
      // Only log once to avoid spam
      return;
    }

    // Get the prefix for this guild - check map first for performance
    let prefix = guildPrefixes.get(message.guild.id);
    if (!prefix) {
      // If not in map, get from database and store for future use
      prefix = await dataManager.getGuildPrefix(message.guild.id);
      guildPrefixes.set(message.guild.id, prefix);
    }

    // Check if message is a command
    if (message.content.startsWith(prefix)) {
      // Parse the command name and arguments
      const args = message.content.slice(prefix.length).trim().split(/ +/);
      const commandName = args.shift().toLowerCase();

      // Check if the command exists in the text commands collection
      if (!client.textCommands) {
        //@ts-ignore
        client.textCommands = new Map();
      }

      const command = client.textCommands.get(commandName);

      if (!command) return;

      // Check command permissions
      if (command.devOnly && !isDeveloper(message.author.id)) {
        return message
          .reply({
            content: `${EMOJI[client.user.id].CROSS} This command is only available to bot developers.`,
            allowedMentions: { repliedUser: false },
          })
          .catch(err => logger.error('Error replying to unauthorized command:', err));
      }

      if (command.adminOnly && !message.member.permissions.has(PermissionFlagsBits.Administrator)) {
        return message
          .reply({
            content: `${EMOJI[client.user.id].CROSS} This command requires Administrator permission.`,
            allowedMentions: { repliedUser: false },
          })
          .catch(err => logger.error('Error replying to unauthorized command:', err));
      }

      // Check if command requires arguments
      //@ts-ignore
      if (command.args && !args.length) {
        let reply = `You didn't provide any arguments, ${message.author}!`;

        if (command.usage) {
          reply += `\nThe proper usage would be: \`${prefix}${command.name} ${command.usage}\``;
        }

        return message.reply(reply);
      }

      // Execute the command
      try {
        await command.execute(message, args, client);

        // Increment command counter
        if (global.incrementCommandCount) {
          global.incrementCommandCount();
        }
      } catch (error) {
        logger.error(`Error executing command ${commandName}:`, error);
        message
          .reply({
            content: 'There was an error executing that command.',
            allowedMentions: { repliedUser: false },
          })
          .catch(err => logger.error('Error sending error message:', err));
      }
      return;
    }
  } catch (error) {
    logger.error(`Error processing message: ${error.message}`);
  }

  // Only show quick help if the bot was mentioned without a command

  if (
    (message.content.includes('@everyone') || message.content.includes('@here')) &&
    !message.content.includes(client.user.id)
  )
    return;

  // Only respond if the bot is directly mentioned (not if a role is mentioned or if it's a reply)
  const isMentioned = message.mentions.has(client.user);

  if (isMentioned) {
    try {
      // Get the prefix for this guild if not already fetched
      let prefix = guildPrefixes.get(message.guild.id);
      if (!prefix) {
        prefix = await dataManager.getGuildPrefix(message.guild.id);
        guildPrefixes.set(message.guild.id, prefix);
      }

      // Create a compact help embed
      const helpEmbed = new EmbedBuilder()
        .setColor(0x5865f2) // Discord Blurple
        .setTitle('MyVC Bot Help')
        .setDescription('Create your own temporary voice channels!')
        .setThumbnail(client.user.displayAvatarURL())
        .addFields(
          {
            name: '🛠️ Setup (Admin)',
            value: 'Use `/setup` to set up the MyVC system on your server',
          },
          {
            name: '🔰 Quick Start',
            value: 'Join the "JOIN_TO_CREATE" channel to create your own voice channel',
          },
          {
            name: '⚙️ Controls',
            value:
              'Use the buttons in the interface channel to customize your channel (name, limit, privacy, etc.)',
          },
          {
            name: '📚 Detailed Help',
            value: `Use \`/help\` or \`${prefix}help\` for command information`,
          }
        )
        .setFooter({
          text: 'Your settings are automatically saved for future channels',
          iconURL: client.user.displayAvatarURL(),
        });

      await message
        .reply({
          embeds: [helpEmbed],
          allowedMentions: { repliedUser: false },
        })
        .catch(error => {
          logger.error('Failed to reply to mention:', error);
        });
    } catch (error) {
      logger.error('Error creating help embed:', error);
    }
  }
};
