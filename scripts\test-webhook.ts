#!/usr/bin/env node
/**
 * Webhook Test Script
 * 
 * This script tests Discord webhooks by sending a test message.
 * It can be used to verify that webhook URLs are correctly configured.
 */
import fetch from 'node-fetch';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get webhook URLs from environment or command line
const errorWebhookUrl = process.env.ERROR_WEBHOOK_URL || '';
const guildWebhookUrl = process.env.GUILD_WEBHOOK_URL || '';

// Parse command line arguments
const args = process.argv.slice(2);
let webhookType = 'both';
let customMessage = 'Test message from MyVC Bot';

// Process arguments
for (let i = 0; i < args.length; i++) {
  if (args[i] === '--type' && i + 1 < args.length) {
    webhookType = args[i + 1].toLowerCase();
    i++;
  } else if (args[i] === '--message' && i + 1 < args.length) {
    customMessage = args[i + 1];
    i++;
  } else if (args[i] === '--help') {
    console.log(`
Webhook Test Script

Usage:
  ts-node test-webhook.ts [options]

Options:
  --type <type>     Webhook type to test (error, guild, or both)
  --message <msg>   Custom message to send
  --help            Show this help message

Examples:
  ts-node test-webhook.ts
  ts-node test-webhook.ts --type error
  ts-node test-webhook.ts --type guild --message "Hello from the bot!"
    `);
    process.exit(0);
  }
}

/**
 * Tests a webhook by sending a message
 * @param url The webhook URL
 * @param name The name of the webhook (for logging)
 * @param message The message to send
 */
async function testWebhook(url: string, name: string, message: string): Promise<void> {
  console.log(`Testing ${name} webhook...`);
  
  if (!url) {
    console.error(`❌ ${name} webhook URL not found in environment variables`);
    return;
  }
  
  try {
    // Validate URL format
    if (!url.startsWith('https://discord.com/api/webhooks/') || url.split('/').length < 7) {
      console.error(`❌ Invalid ${name} webhook URL format`);
      console.error('Webhook URL should be in the format: https://discord.com/api/webhooks/{id}/{token}');
      return;
    }
    
    // Extract ID and token for logging
    const urlParts = url.split('/');
    const webhookId = urlParts[5];
    const webhookToken = urlParts[6].split('?')[0].split('#')[0];
    
    console.log(`Webhook ID: ${webhookId}`);
    console.log(`Webhook Token: ${webhookToken.substring(0, 5)}...`);
    
    // Prepare payload
    const payload = {
      content: message,
      username: 'MyVC Bot - Test',
      avatar_url: 'https://i.imgur.com/AfFp7pu.png',
    };
    
    // Send the request
    console.log(`Sending test message: "${message}"`);
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });
    
    // Check response
    if (response.ok) {
      console.log(`✅ ${name} webhook test successful!`);
    } else {
      const errorText = await response.text();
      console.error(`❌ ${name} webhook test failed: ${response.status} ${response.statusText}`);
      console.error(`Error details: ${errorText}`);
      
      // Provide troubleshooting advice based on error code
      if (response.status === 404) {
        console.error('This usually means the webhook does not exist or has been deleted.');
      } else if (response.status === 401 || response.status === 403) {
        console.error('This usually means the webhook token is invalid.');
      } else if (response.status === 429) {
        console.error('Rate limit exceeded. Try again later.');
      } else if (response.status === 405) {
        console.error('Method not allowed. This usually means the webhook URL is incorrect.');
      }
    }
  } catch (error) {
    console.error(`❌ Error testing ${name} webhook:`, error);
  }
  
  console.log(); // Empty line for readability
}

// Main function
async function main() {
  console.log('MyVC Bot Webhook Test');
  console.log('=====================');
  
  if (webhookType === 'error' || webhookType === 'both') {
    await testWebhook(errorWebhookUrl, 'Error', customMessage);
  }
  
  if (webhookType === 'guild' || webhookType === 'both') {
    await testWebhook(guildWebhookUrl, 'Guild', customMessage);
  }
  
  console.log('Webhook testing complete.');
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
