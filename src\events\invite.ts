import {
  ActionRowBuilder,
  UserSelectMenuBuilder,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
} from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';
import { SERVER_INVITE_LINK } from '../constants';
import { isDeveloper } from '../constants/devs';

export const name = 'invite';

// In-memory cooldown map: { inviterId: { invitedUserId: timestamp } }
const inviteCooldowns: Record<string, Record<string, number>> = {};
const INVITE_COOLDOWN_MS = 60 * 1000; // 1 minute

// Called when the "Invite" button is clicked
// Interaction is deferred, ownership & cooldown checked in interactionCreate.ts
export const execute = async (interaction, client, userChannel) => {
  // Sends the user select menu
  try {
    const inviteSelect = new UserSelectMenuBuilder()
      .setCustomId(`invite_select_${userChannel.id}`) // Consistent ID format
      .setPlaceholder('Select user(s) to invite')
      .setMinValues(1)
      .setMaxValues(10); // Allow inviting multiple users

    const row: any = new ActionRowBuilder().addComponents(inviteSelect);

    // Use replyOrFollowUpEphemeral as interaction is deferred by the central handler
    await replyOrFollowUpEphemeral(interaction, {
      content: 'Select user(s) to invite to your channel. They will be automatically trusted:',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending invite select menu: ${error.message}`);
    await handleInteractionError('invite.execute', error, interaction, client);
  }
};

// Called when users are selected from the menu sent above
export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `invite.handleSelectMenu:selectMenu:${interaction.customId}`;
  // Cooldown & ownership checked in interactionCreate.ts before calling this
  if (!interaction.isUserSelectMenu()) return; // Type guard

  try {
    const selectedUsers = interaction.users; // Map<string, User>
    const ownerId = interaction.user.id;
    const invitedUserTags: string[] = [];
    const failedInviteTags: string[] = [];
    let trustErrorsOccurred = false;

    logger.debug(
      `${menuSource}: User ${ownerId} selected ${selectedUsers.size} user(s) to invite to channel ${targetChannel.id}`
    );

    // Fetch settings (assuming async)
    const settings = (await dataManager.getUserSettings(ownerId)) || { trustedUsers: [] };
    settings.trustedUsers = settings.trustedUsers || [];

    const channelUrl = `https://discord.com/channels/${interaction.guild.id}/${targetChannel.id}`;

    for (const [selectedUserId, selectedUser] of selectedUsers) {
      // Cooldown check: skip if inviter has invited this user within cooldown
      const now = Date.now();
      if (!inviteCooldowns[ownerId]) inviteCooldowns[ownerId] = {};
      const lastInvite = inviteCooldowns[ownerId][selectedUserId] || 0;
      if (now - lastInvite < INVITE_COOLDOWN_MS) {
        const remaining = Math.ceil((INVITE_COOLDOWN_MS - (now - lastInvite)) / 1000);
        failedInviteTags.push(`${selectedUser.tag} (invite cooldown: ${remaining}s left)`);
        continue;
      }
      // Passed cooldown, update timestamp
      inviteCooldowns[ownerId][selectedUserId] = now;
      // Clean up old cooldowns for this inviter
      for (const [uid, ts] of Object.entries(inviteCooldowns[ownerId])) {
        if (now - ts > INVITE_COOLDOWN_MS) delete inviteCooldowns[ownerId][uid];
      }

      if (selectedUserId === ownerId && !isDeveloper(ownerId)) continue;
      if (selectedUser.bot) {
        logger.warn(`${menuSource}: User ${ownerId} attempted to invite bot ${selectedUser.tag}.`);
        failedInviteTags.push(`${selectedUser.tag} (bot)`);
        continue;
      }

      // 1. Trust the user
      try {
        if (!settings.trustedUsers.includes(selectedUserId)) {
          settings.trustedUsers.push(selectedUserId);
        }
        await targetChannel.permissionOverwrites.edit(
          selectedUserId,
          {
            ViewChannel: true,
            Connect: true,
            Speak: true,
            // Stream: true,
            // UseEmbeddedActivities: true,
          },
          `Trusted & Invited by channel owner ${interaction.user.tag}`
        );
        logger.debug(`Trusted user ${selectedUser.tag} (${selectedUserId}) for invite`);
      } catch (trustError) {
        logger.error(
          `${menuSource}: Failed to trust user ${selectedUserId} (${selectedUser.tag}) during invite: ${trustError.message}`
        );
        trustErrorsOccurred = true;
        failedInviteTags.push(`${selectedUser.tag} (permission error)`);
        continue;
      }

      // 2. Send DM invite
      const inviteEmbed = new EmbedBuilder()
        .setColor(0x5865f2)
        .setTitle('Voice Channel Invitation')
        .setDescription(`${interaction.user.tag} invited you to join their voice channel!`)
        .addFields(
          { name: 'Channel', value: targetChannel.name, inline: true },
          { name: 'Server', value: interaction.guild.name, inline: true },
          { name: 'Direct Link', value: `[Click here to join](${channelUrl})`, inline: false }
        )
        .setTimestamp();

      const inviteButton = new ButtonBuilder()
        .setLabel('Join Channel')
        .setStyle(ButtonStyle.Link)
        .setURL(`https://discord.com/channels/${interaction.guild.id}/${targetChannel.id}`)
        .setEmoji(EMOJI[client.user.id].JOIN);

      const supportButton = new ButtonBuilder()
        .setLabel('Support Server')
        .setStyle(ButtonStyle.Link)
        .setURL(SERVER_INVITE_LINK)
        .setEmoji(EMOJI[client.user.id].SUPPORT);

      const inviteRow: any = new ActionRowBuilder().addComponents(inviteButton, supportButton);

      try {
        const member = await interaction.guild.members.fetch(selectedUserId).catch(() => null);
        if (member) {
          await member.send({
            content: `Invitation from ${interaction.user.tag}:`,
            embeds: [inviteEmbed],
            components: [inviteRow],
          });
          invitedUserTags.push(selectedUser.tag);
          logger.debug(`Sent invite DM to ${selectedUser.tag} (${selectedUserId})`);
        } else {
          logger.warn(`${menuSource}: Could not fetch member ${selectedUserId} to send DM invite.`);
          failedInviteTags.push(`${selectedUser.tag} (not found)`);
        }
      } catch (dmError) {
        logger.warn(`${menuSource}: Failed to DM user ${selectedUserId}: ${dmError.message}`);
        failedInviteTags.push(`${selectedUser.tag} (DMs disabled?)`);
        invitedUserTags.push(`${selectedUser.tag} (trusted, DM failed)`);
      }
    }

    // Save updated settings (assuming async)
    // await dataManager.setUserSettings(ownerId, settings);
    logger.debug(`Updated trusted users settings for owner ${ownerId} after invites`);

    // Construct final reply
    let replyMessage = '';
    if (invitedUserTags.length > 0) {
      replyMessage += `${EMOJI[client.user.id].CHECK} Invited & Trusted: ${invitedUserTags.join(', ')}.`;
    }
    if (failedInviteTags.length > 0) {
      replyMessage +=
        (replyMessage ? '\n' : '') +
        `⚠️ Could not invite: ${failedInviteTags.join(', ')}.\n` +
        `You cannot invite the same user again for 1 minute. Please wait for the cooldown to expire before re-inviting.`;
    }
    if (trustErrorsOccurred) {
      replyMessage +=
        (replyMessage ? '\n' : '') + '⚠️ Errors occurred granting permissions for some users.';
    }
    if (!replyMessage) {
      replyMessage = 'No users were invited (bots/yourself cannot be invited).';
    }

    // Final reply handled here
    await replyOrFollowUpEphemeral(interaction, {
      content: replyMessage,
      components: [],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle invite select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};
