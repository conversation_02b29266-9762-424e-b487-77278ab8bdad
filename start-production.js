#!/usr/bin/env node
/**
 * TempVoice Discord Bot - Production Start Script
 * This script starts the bot in production mode with proper error handling
 */

// Import required Node.js modules
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Set environment to production
process.env.NODE_ENV = process.env.NODE_ENV || 'production';
process.env.SHARDED = process.env.SHARDED || 'false';

// Set Node.js memory optimization flags for Railway
process.env.NODE_OPTIONS = process.env.NODE_OPTIONS || '--max-old-space-size=512 --optimize-for-size --gc-interval=100';

console.log('Starting TempVoice Discord Bot in production mode...');
console.log('Environment:', process.env.NODE_ENV);
console.log('Sharded mode:', process.env.SHARDED);

// Build the project to ensure we have the latest compiled code
console.log('Building project...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('Build completed successfully.');
} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}

// Check if the dist directory and main files exist after build
const distDir = path.join(__dirname, 'dist');
const srcDir = path.join(distDir, 'src');
const indexFile = path.join(srcDir, 'index.js');

if (!fs.existsSync(distDir)) {
  console.error('Error: dist directory was not created by the build.');
  process.exit(1);
}

if (!fs.existsSync(srcDir)) {
  console.error('Error: dist/src directory was not created by the build.');
  process.exit(1);
}

if (!fs.existsSync(indexFile)) {
  console.error('Error: src/index.js was not compiled. Please check your tsconfig.json configuration.');
  console.error('Expected file path:', indexFile);
  process.exit(1);
}

// Validate required environment variables
const requiredEnvVars = ['TOKEN', 'CLIENT_ID', 'MONGODB_URI'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error('Error: Missing required environment variables:', missingEnvVars.join(', '));
  console.error('Please set these environment variables before starting the bot.');
  process.exit(1);
}

// Set up graceful shutdown
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

// Start the bot with error handling
try {
  console.log('Starting bot...');
  require('./dist/src/index.js');
} catch (error) {
  console.error('Failed to start bot:', error);
  process.exit(1);
}
