{"name": "tempvc", "version": "1.1.0", "description": "Discord bot for creating temporary voice channels with webhook logging", "main": "index.js", "scripts": {"start": "node --max-old-space-size=512 --optimize-for-size --gc-interval=100 dist/src/index.js", "start:dev": "ts-node src/index.ts", "start:prod": "node start-production.js", "start:railway": "npm run build && node --max-old-space-size=512 --optimize-for-size --gc-interval=100 dist/src/index.js", "start:gcp": "npm run build && node --max-old-space-size=1024 --optimize-for-size dist/src/index.js", "build": "tsc", "build:diagnostic": "node build.js", "start:node": "node start.js", "start:sharded": "node start-sharded.js", "dev": "nodemon --config nodemon.json", "test": "echo \"Error: no test specified\" && exit 1", "deploy": "ts-node scripts/register-commands.ts", "clear-db": "ts-node src/scripts/clearDb.ts", "logs:guild": "ts-node scripts/view-guild-logs.ts", "test:webhook": "ts-node scripts/test-webhook.ts", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "format:all": "prettier --write .", "lint": "eslint \"src/**/*.ts\"", "lint:fix": "eslint \"src/**/*.ts\" --fix"}, "author": "", "license": "ISC", "dependencies": {"discord.js": "^14.11.0", "dotenv": "^16.4.7", "mongoose": "^8.13.2", "node-fetch": "^2.7.0", "winston": "^3.10.0"}, "devDependencies": {"@types/node": "^22.13.13", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "eslint": "^9.25.1", "eslint-plugin-unused-imports": "^4.1.4", "nodemon": "^3.0.1", "prettier": "^3.5.3", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}