# Dependencies
node_modules/
package-lock.json
yarn.lock

# Environment files
.env
.env.*
!.env.example

# Build outputs
dist/
build/
out/
*.tsbuildinfo

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
debug.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Testing and coverage
coverage/
.nyc_output/

# IDE and editor files
.idea/
.vscode/
*.sublime-workspace
*.sublime-project
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# MongoDB
data/
mongodb/

# Temporary files
*.tmp
*.temp
.cache/