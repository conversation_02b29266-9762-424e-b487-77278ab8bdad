import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';
import { GuildMember, VoiceChannel, EmbedBuilder } from 'discord.js';
import dataManager from '../utils/dataManager';

export const name = 'claim';

export const execute = async (interaction, client) => {
  const claimSource = `claim.execute:button:${interaction.customId}`;
  const guild = interaction.guild;
  const member = interaction.member as GuildMember;
  const userId = interaction.user.id;

  const tempChannelLastSeen: Map<string, number> = client.tempChannelLastSeen || new Map();

  if (!guild || !member) {
    await replyOrFollowUpEphemeral(interaction, {
      content: `${EMOJI[client.user.id].CROSS} This command can only be used in a server.`,
    });
    return;
  }

  if (!member.voice.channel) {
    await replyOrFollowUpEphemeral(interaction, {
      content: `${EMOJI[client.user.id].CROSS} You need to be in a voice channel to claim it.`,
    });
    return;
  }

  const targetChannel = member.voice.channel as VoiceChannel;

  if (!client.tempChannels.has(targetChannel.id)) {
    await replyOrFollowUpEphemeral(interaction, {
      content: `${EMOJI[client.user.id].CROSS} This channel is not a claimable temporary channel.`,
    });
    return;
  }

  const currentOwnerId = client.tempChannels.get(targetChannel.id);
  let canClaim = false;
  let previousOwnerId: string | null = null;

  if (currentOwnerId) {
    previousOwnerId = currentOwnerId;
    const ownerInChannel = targetChannel.members.has(currentOwnerId);

    if (ownerInChannel) {
      const ownerMember = targetChannel.members.get(currentOwnerId);
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} This channel is already owned by ${ownerMember?.user?.tag || 'someone'} who is currently in the channel.`,
      });
      return;
    } else {
      const ownerMember = await guild.members.fetch(currentOwnerId).catch(() => null);
      if (!ownerMember) {
        logger.warn(
          `${claimSource}: Channel ${targetChannel.id} owner ${currentOwnerId} not found in guild. Allowing claim by ${userId}.`
        );
        canClaim = true;
      } else {
        const lastSeen = tempChannelLastSeen.get(targetChannel.id);
        const ownerAbsentDuration = lastSeen ? Date.now() - lastSeen : Infinity;
        const ONE_MINUTE = 60 * 1000;

        if (ownerAbsentDuration > ONE_MINUTE) {
          logger.info(
            `${claimSource}: Channel ${targetChannel.id} owner ${currentOwnerId} has been absent for ${ownerAbsentDuration / 1000}s (> 60s). Allowing claim by ${userId}.`
          );
          canClaim = true;
        } else {
          await replyOrFollowUpEphemeral(interaction, {
            content: `${EMOJI[client.user.id].CROSS} The owner (${ownerMember.user.tag}) has only been absent for ${Math.round(ownerAbsentDuration / 1000)} seconds. Please wait at least 1 minute before claiming.`,
          });
          return;
        }
      }
    }
  } else {
    canClaim = true;
  }

  if (canClaim) {
    try {
      logger.info(
        `User ${interaction.user.tag} (${userId}) is claiming channel ${targetChannel.name} (${targetChannel.id}) ${previousOwnerId ? `from previous owner ${previousOwnerId}` : ''}`
      );

      if (previousOwnerId) {
        try {
          await targetChannel.permissionOverwrites.delete(
            previousOwnerId,
            `Channel claimed by ${interaction.user.tag}`
          );
          logger.info(
            `${claimSource}: Removed permission overwrites for previous owner ${previousOwnerId} on channel ${targetChannel.id}.`
          );
        } catch (permError) {
          logger.warn(
            `${claimSource}: Failed to remove permissions for previous owner ${previousOwnerId} on ${targetChannel.id}: ${permError.message}`
          );
        }
      }

      client.tempChannels.set(targetChannel.id, userId);

      tempChannelLastSeen.delete(targetChannel.id);

      try {
        logger.info(
          `${claimSource}: Resetting channel ${targetChannel.id} to default configuration`
        );

        const defaultName = `${member.user.username}'s channel`;
        await targetChannel.setName(defaultName);

        await targetChannel.setUserLimit(0);
        await targetChannel.setBitrate(64000);

        logger.info(`${claimSource}: Successfully reset channel configuration to defaults`);
      } catch (resetError) {
        logger.error(
          `${claimSource}: Failed to reset channel configuration: ${resetError.message}`
        );
      }

      let newOwnerConfig = null;
      try {
        newOwnerConfig = await dataManager.getUserSettings(userId);
        logger.info(`${claimSource}: Retrieved config for new owner ${userId}`);
      } catch (configError) {
        logger.error(`${claimSource}: Failed to retrieve new owner config: ${configError.message}`);
      }

      await targetChannel.permissionOverwrites.edit(
        userId,
        {
          ViewChannel: true,
          Connect: true,
          Speak: true,
        },
        { reason: `Claimed by ${interaction.user.tag}` }
      );

      if (newOwnerConfig) {
        try {
          if (newOwnerConfig.channelNameFormat) {
            const formattedName = newOwnerConfig.channelNameFormat
              .replace('{username}', member.user.username)
              .replace('{tag}', member.user.tag);

            await targetChannel.setName(formattedName);
            logger.info(`${claimSource}: Updated channel name to ${formattedName}`);
          }

          if (newOwnerConfig.userLimit !== undefined) {
            await targetChannel.setUserLimit(newOwnerConfig.userLimit);
          }

          if (newOwnerConfig.bitrate !== undefined) {
            await targetChannel.setBitrate(newOwnerConfig.bitrate);
          }

          logger.info(
            `${claimSource}: Applied new owner's settings to channel ${targetChannel.id}`
          );
        } catch (settingsError) {
          logger.error(`${claimSource}: Failed to apply new settings: ${settingsError.message}`);
        }
      }

      try {
        await dataManager.saveChannelSettings({
          [targetChannel.id]: {
            ownerId: userId,
            guildId: guild.id,
            lastUpdated: new Date(),
          },
        });
        logger.info(
          `${claimSource}: Updated channel owner in database for channel ${targetChannel.id}`
        );
      } catch (dbError) {
        logger.error(
          `${claimSource}: Failed to update database for channel ${targetChannel.id}: ${dbError.message}`
        );
      }

      const successEmbed = new EmbedBuilder()
        .setColor('#00FF00')
        .setTitle(`${EMOJI[client.user.id].CHECK} Channel Claimed`)
        .setDescription(`You have successfully claimed the channel **${targetChannel.name}**!`)
        .setTimestamp();

      await replyOrFollowUpEphemeral(interaction, {
        embeds: [successEmbed],
      });
    } catch (error) {
      logger.error(
        `${claimSource}: Failed to claim channel ${targetChannel.id} for user ${userId}: ${error.message}`
      );

      if (client.tempChannels.get(targetChannel.id) === userId) {
        client.tempChannels.set(targetChannel.id, previousOwnerId);
      }
      await handleInteractionError(claimSource, error, interaction, client);
    }
  }
};
