import { ActionRowBuilder, StringSelectMenuBuilder } from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'untrust';

export const execute = async (interaction, client, userChannel) => {
  try {
    const ownerId = interaction.user.id;

    const settings = (await dataManager.getUserSettings(ownerId)) || { trustedUsers: [] };
    const trustedUserIds = settings.trustedUsers || [];

    if (trustedUserIds.length === 0) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} You have no users currently trusted.`,
      });
      return;
    }

    const validTrustedUsers = [];
    for (const userId of trustedUserIds) {
      try {
        if (userId === client.user.id) continue;

        const user = await client.users.fetch(userId);
        validTrustedUsers.push({
          id: userId,
          tag: user.tag,
          username: user.username,
        });
      } catch {
        logger.warn(`Removing non-existent user ${userId} from trusted list of ${ownerId}`);
        settings.trustedUsers = settings.trustedUsers.filter(id => id !== userId);
      }
    }

    if (validTrustedUsers.length === 0) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} No valid trusted users found to untrust.`,
      });
      return;
    }

    const untrustSelect = new StringSelectMenuBuilder()
      .setCustomId(`untrust_select_${userChannel.id}`)
      .setPlaceholder('Select user(s) to untrust')
      .setMinValues(1)
      .setMaxValues(validTrustedUsers.length > 10 ? 10 : validTrustedUsers.length);

    for (const user of validTrustedUsers) {
      untrustSelect.addOptions({
        label: user.tag,
        description: `User ID: ${user.id}`,
        value: user.id,
      });
    }

    const row: any = new ActionRowBuilder().addComponents(untrustSelect);

    await replyOrFollowUpEphemeral(interaction, {
      content: 'Select user(s) to remove from your trusted list:',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending untrust select menu: ${error.message}`);
    await handleInteractionError('untrust.execute', error, interaction, client);
  }
};

export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `untrust.handleSelectMenu:selectMenu:${interaction.customId}`;

  if (!interaction.isStringSelectMenu()) return;

  try {
    const selectedUserIds = interaction.values;
    const ownerId = interaction.user.id;
    const untrustedUserTags: string[] = [];
    let errorsOccurred = false;

    logger.debug(
      `${menuSource}: User ${ownerId} selected ${selectedUserIds.length} user(s) to untrust for channel ${targetChannel.id}`
    );

    const settings = (await dataManager.getUserSettings(ownerId)) || { trustedUsers: [] };
    settings.trustedUsers = settings.trustedUsers || [];
    let settingsChanged = false;

    for (const selectedUserId of selectedUserIds) {
      if (!settings.trustedUsers.includes(selectedUserId)) {
        logger.warn(
          `${menuSource}: User ${selectedUserId} selected for untrust, but not found in settings for owner ${ownerId}.`
        );
        continue;
      }

      try {
        const selectedUser = await client.users.fetch(selectedUserId).catch(() => null);

        settings.trustedUsers = settings.trustedUsers.filter(id => id !== selectedUserId);
        settingsChanged = true;
        logger.debug(`Removed ${selectedUserId} from trusted list for owner ${ownerId}`);

        let isInGuild = false;
        try {
          const guildMember = await targetChannel.guild.members
            .fetch(selectedUserId)
            .catch(() => null);
          isInGuild = !!guildMember;
        } catch (error) {
          logger.debug(
            `${menuSource}: Could not determine if user ${selectedUserId} is in guild: ${error.message}`
          );
        }

        if (isInGuild) {
          const overwrite = targetChannel.permissionOverwrites.cache.get(selectedUserId);
          if (overwrite) {
            await overwrite.delete(`Untrusted by channel owner ${interaction.user.tag}`);
            logger.debug(`Removed permission overwrite for untrusted user ${selectedUserId}`);
          } else {
            logger.debug(
              `${menuSource}: No permission overwrite found for user ${selectedUserId} in channel ${targetChannel.id}, but user is in guild.`
            );
          }
        } else {
          logger.debug(
            `${menuSource}: User ${selectedUserId} is not in guild, skipping permission overwrite deletion.`
          );
        }

        untrustedUserTags.push(selectedUser ? selectedUser.tag : selectedUserId);
      } catch (userUntrustError) {
        logger.error(
          `${menuSource}: Failed to untrust user ${selectedUserId}: ${userUntrustError.message}`
        );
        errorsOccurred = true;
      }
    }

    if (settingsChanged) {
      try {
        const settingsObj = { [ownerId]: settings };
        await dataManager.saveUserSettings(settingsObj);
        logger.debug(
          `Updated trusted users settings for owner ${ownerId} in MongoDB after untrust`
        );
      } catch (error) {
        logger.error(`Failed to save updated trusted users to MongoDB: ${error.message}`);

        global.saveUserSettings(ownerId);
        logger.debug(`Falling back to queue-based saving for untrust operation`);
      }
    }

    let replyMessage = '';
    if (untrustedUserTags.length > 0) {
      replyMessage += `${EMOJI[client.user.id].CHECK} Untrusted ${untrustedUserTags.join(', ')}.`;
    }
    if (errorsOccurred) {
      replyMessage +=
        (replyMessage ? '\n' : '') +
        '⚠️ Errors occurred while untrusting some users. Check bot permissions.';
    }
    if (!replyMessage) {
      replyMessage =
        'No users were untrusted (possibly due to errors or selecting non-trusted users).';
    }

    await replyOrFollowUpEphemeral(interaction, {
      content: replyMessage,
      components: [],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle untrust select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};
