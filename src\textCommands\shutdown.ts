/**
 * Shutdown Command
 * Allows bot developers to shut down the bot gracefully
 */
import { EmbedBuilder, ActivityType } from 'discord.js';
import { Message, Client } from 'discord.js';
import logger from '../utils/logger';
import dataManager from '../utils/dataManager';

export const name = 'shutdown';
export const description = 'Shuts down the bot gracefully';
export const usage = 'shutdown [reason]';
export const devOnly = true;
export const adminOnly = false;

export const execute = async (message: Message, args: string[], client: Client) => {
  try {
    const reason = args.length > 0 ? args.join(' ') : 'No reason provided';

    const embed = new EmbedBuilder()
      .setColor(0xff0000)
      .setTitle('Bot Shutdown')
      .setDescription('The bot is shutting down...')
      .addFields(
        { name: 'Reason', value: reason },
        { name: 'Initiated By', value: `${message.author.tag} (${message.author.id})` }
      )
      .setFooter({
        text: 'Performing graceful shutdown',
        iconURL: client.user.displayAvatarURL(),
      })
      .setTimestamp();

    await message.reply({ embeds: [embed], allowedMentions: { repliedUser: false } });

    logger.warn(
      `Bot is shutting down - Initiated by ${message.author.tag} (${message.author.id}). Reason: ${reason}`
    );

    await client.user.setStatus('dnd');
    await client.user.setActivity('Shutting down...', { type: ActivityType.Playing });

    logger.info('Saving all data before shutdown...');

    if (global.saveUserSettings) {
      global.saveUserSettings();
    }

    if (dataManager.cleanup) {
      dataManager.cleanup();
    }

    setTimeout(() => {
      logger.info('Shutdown complete. Exiting process.');
      process.exit(0);
    }, 1500);
  } catch (error) {
    logger.error('Error in shutdown command:', error);
    message
      .reply({
        content: 'An error occurred while shutting down the bot.',
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
