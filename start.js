#!/usr/bin/env node
/**
 * TempVoice Discord Bot - Standard Start Script
 * This script starts the bot in normal (non-sharded) mode
 */

// Import required Node.js modules
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Set environment to indicate we're NOT in sharded mode
process.env.SHARDED = 'false';

console.log('Starting TempVoice Discord Bot in normal mode...');

// Build the project to ensure we have the latest compiled code
console.log('Building project...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('Build completed successfully.');
} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
}

// Check if the dist directory and main files exist after build
const distDir = path.join(__dirname, 'dist');
const srcDir = path.join(distDir, 'src');
const indexFile = path.join(srcDir, 'index.js');

if (!fs.existsSync(distDir)) {
  console.error('Error: dist directory was not created by the build.');
  process.exit(1);
}

if (!fs.existsSync(srcDir)) {
  console.error('Error: dist/src directory was not created by the build.');
  process.exit(1);
}

if (!fs.existsSync(indexFile)) {
  console.error('Error: src/index.js was not compiled. Please check your tsconfig.json configuration.');
  console.error('Expected file path:', indexFile);
  process.exit(1);
}

// Start the bot
try {
  console.log('Starting bot...');
  require('./dist/src/index.js');
} catch (error) {
  console.error('Failed to start bot:', error);
  process.exit(1);
} 