/**
 * Help Command
 * Displays available text commands and their usage
 */
import { EmbedBuilder } from 'discord.js';
import dataManager from '../utils/dataManager';
import { isDeveloper } from '../constants/devs';
import logger from '../utils/logger';
import { EMOJI } from '../constants/emoji';

const guildPrefixes = new Map();

dataManager.on('guildPrefixChanged', (guildId, newPrefix) => {
  guildPrefixes.set(guildId, newPrefix);
});

dataManager.on('guildSettingsChanged', (guildId, settings) => {
  if (settings.prefix) {
    guildPrefixes.set(guildId, settings.prefix);
  }
});

export const name = 'help';
export const description = 'Displays available text commands and their usage';
export const usage = 'help [command_name]';
export const devOnly = false;
export const adminOnly = false;

export const execute = async (message, args, client) => {
  try {
    let prefix = guildPrefixes.get(message.guild.id);
    if (!prefix) {
      prefix = await dataManager.getGuildPrefix(message.guild.id);
      guildPrefixes.set(message.guild.id, prefix);
    }

    if (args.length > 0) {
      const commandName = args[0].toLowerCase();
      const command = client.textCommands.get(commandName);

      if (!command) {
        return message.reply({
          content: `${EMOJI[client.user.id].CROSS} Command not found: \`\`\`${commandName}\`\`\``,
          allowedMentions: { repliedUser: false },
        });
      }

      if (command.devOnly && !isDeveloper(message.author.id)) {
        return message.reply({
          content: `${EMOJI[client.user.id].CROSS} You do not have permission to view this command.`,
          allowedMentions: { repliedUser: false },
        });
      }

      const embed = new EmbedBuilder()
        .setColor(0x5865f2)
        .setTitle(`Command: ${command.name}`)
        .setDescription(
          `Prefix: \`\`\`${prefix}\`\`\`\n${command.description || 'No description available'}`
        )
        .setFooter({
          text: `Requested by ${message.author.tag}`,
          iconURL: message.author.displayAvatarURL(),
        })
        .setTimestamp();

      if (command.usage) {
        embed.addFields({
          name: 'Usage',
          value: `\`\`\`${prefix}${command.usage}\`\`\``,
        });
      }

      let permissionInfo = 'Everyone';
      if (command.devOnly) {
        permissionInfo = 'Bot Developer only';
      } else if (command.adminOnly) {
        permissionInfo = 'Server Administrator only';
      }

      embed.addFields({
        name: 'Permission',
        value: permissionInfo,
      });

      if (commandName === 'prefix') {
        embed.addFields({
          name: 'Usage',
          value:
            '```\n' +
            `${prefix}prefix\n` +
            'Show current prefix\n\n' +
            `${prefix}prefix set <new_prefix>\n` +
            'Change the prefix' +
            '```',
        });
      }

      const sentSpecificHelp = await message.reply({
        embeds: [embed],
        allowedMentions: { repliedUser: false },
      });
      setTimeout(() => {
        sentSpecificHelp
          .delete()
          .catch(err => logger.error('Failed to delete specific help message:', err));
      }, 60000);
      return;
    }

    const commandGroups = {
      regular: [],
      admin: [],
      dev: [],
    };

    for (const [_, command] of client.textCommands) {
      if (command.devOnly) {
        if (isDeveloper(message.author.id)) {
          commandGroups.dev.push(command);
        }
      } else if (command.adminOnly) {
        if (message.member.permissions.has('Administrator') || isDeveloper(message.author.id)) {
          commandGroups.admin.push(command);
        }
      } else {
        commandGroups.regular.push(command);
      }
    }

    const embed = new EmbedBuilder()
      .setColor(0x5865f2)
      .setTitle('MyVC Bot Commands')
      .setDescription(
        `Use \`/setup\` to configure the bot.\n\nUse \`${prefix}help <command>\` for detailed information about a specific text command. For a general overview of bot features, try the \`/help\` slash command.\nCurrent prefix: \`\`\`${prefix}\`\`\``
      )
      .setThumbnail(client.user.displayAvatarURL())
      .setFooter({
        text: `Requested by ${message.author.tag}`,
        iconURL: message.author.displayAvatarURL(),
      })
      .setTimestamp();

    if (commandGroups.regular.length > 0) {
      const regularCommands = commandGroups.regular
        .sort((a, b) => a.name.localeCompare(b.name))
        .map(cmd => `\`${prefix}${cmd.name}\`: ${cmd.description || 'No description available'}`)
        .join('\n');

      embed.addFields({
        name: '📋 Regular Commands',
        value: regularCommands,
      });
    }

    if (commandGroups.admin.length > 0) {
      const adminCommands = commandGroups.admin
        .sort((a, b) => a.name.localeCompare(b.name))
        .map(cmd => `\`${prefix}${cmd.name}\`: ${cmd.description || 'No description available'}`)
        .join('\n');

      embed.addFields({
        name: '🔒 Administrator Commands',
        value: adminCommands,
      });
    }

    if (commandGroups.dev.length > 0) {
      const devCommands = commandGroups.dev
        .sort((a, b) => a.name.localeCompare(b.name))
        .map(cmd => `\`${prefix}${cmd.name}\`: ${cmd.description || 'No description available'}`)
        .join('\n');

      embed.addFields({
        name: '⚙️ Developer Commands',
        value: devCommands,
      });
    }

    const sentGeneralHelp = await message.reply({
      embeds: [embed],
      allowedMentions: { repliedUser: false },
    });
    setTimeout(() => {
      sentGeneralHelp
        .delete()
        .catch(err => logger.error('Failed to delete general help message:', err));
    }, 60000);
    return;
  } catch (error) {
    logger.error('Error in help command:', error);
    return message
      .reply({
        content: 'An error occurred while fetching help information.',
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
