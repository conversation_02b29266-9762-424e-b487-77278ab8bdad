
delete require.cache[require.resolve('dotenv')];
require('dotenv').config({ override: true });
import { REST, Routes } from 'discord.js';
import * as fs from 'node:fs';
import * as path from 'node:path';


function validateCommand(command, filePath) {
  
  if (!command) {
    console.error(`[ERROR] Command at ${filePath} is undefined or null`);
    return false;
  }
  
  
  if (!command.data) {
    console.error(`[ERROR] Command at ${filePath} is missing the required "data" property`);
    return false;
  }
  
  if (!command.execute) {
    console.error(`[ERROR] Command at ${filePath} is missing the required "execute" property`);
    return false;
  }
  
  
  if (!command.data.name) {
    console.error(`[ERROR] Command at ${filePath} has data but is missing "name" property`);
    return false;
  }
  
  
  if (typeof command.data.toJSON !== 'function') {
    console.error(`[ERROR] Command at ${filePath} has data but missing "toJSON" method`);
    return false;
  }
  
  return true;
}


console.log('Environment check:');
console.log(`CLIENT_ID: ${process.env.CLIENT_ID ? process.env.CLIENT_ID.substring(0, 5) + '...' : 'NOT SET'}`);
console.log(`GUILD_ID: ${process.env.GUILD_ID || 'NOT SET (will register globally)'}`);
console.log(`TOKEN: ${process.env.TOKEN ? 'SET (masked for security)' : 'NOT SET'}`);
console.log('Full .env file path:', path.resolve(process.cwd(), '.env'));
console.log('dotenv loaded from:', require.resolve('dotenv'));


const commands = [];


const commandsPath = path.join(__dirname, '..', 'src', 'commands');
console.log(`Looking for commands in: ${commandsPath}`);


if (!fs.existsSync(commandsPath)) {
  console.error(`[ERROR] Commands directory does not exist: ${commandsPath}`);
  process.exit(1);
}


const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.ts'));
console.log(`Found ${commandFiles.length} command files: ${commandFiles.join(', ')}`);


for (const file of commandFiles) {
  const filePath = path.join(commandsPath, file);
  try {
    let command;
    try {
      command = require(filePath);
    } catch (importError) {
      console.error(`[ERROR] Failed to import command from ${filePath}:`, importError);
      continue;
    }

    
    if (validateCommand(command, filePath)) {
      commands.push(command.data.toJSON());
      console.log(`[INFO] Loaded command: ${command.data.name}`);
    } else {
      console.log(`[WARNING] Skipping invalid command at ${filePath}`);
    }
  } catch (error) {
    console.error(`[ERROR] Failed to process command from ${filePath}:`, error);
  }
}

if (commands.length === 0) {
  console.error('[ERROR] No valid commands found to register');
  process.exit(1);
}


const rest = new REST({ version: '10' }).setToken(process.env.TOKEN);


(async () => {
  try {
    console.log(`[INFO] Started refreshing ${commands.length} application (/) commands.`);

    let data;

    
    if (process.env.GUILD_ID) {
      
      console.log(`[INFO] Registering guild commands to guild ID: ${process.env.GUILD_ID}`);

      data = await rest.put(
        Routes.applicationGuildCommands(process.env.CLIENT_ID, process.env.GUILD_ID),
        { body: commands },
      );
      console.log(`[INFO] Successfully registered commands for guild ${process.env.GUILD_ID}`);
    } else {
      
      console.log('[INFO] Registering global commands (may take up to an hour to propagate)');

      data = await rest.put(
        Routes.applicationCommands(process.env.CLIENT_ID),
        { body: commands },
      );
      console.log('[INFO] Successfully registered global commands (may take up to an hour to propagate)');
    }

    console.log(`[INFO] Successfully reloaded ${data.length} application (/) commands.`);
    process.exit(0); 
  } catch (error) {
    console.error('[ERROR] Failed to register commands:', error);

    if (error.code === 50001) {
      console.error('[HINT] Error 50001 suggests missing permissions. Make sure your bot has the applications.commands scope.');
    } else if (error.code === 50013) {
      console.error('[HINT] Error 50013 suggests the bot lacks permissions. Check bot permissions in your server.');
    } else if (error.code === 10002) {
      console.error('[HINT] Error 10002 suggests the specified guild ID is unknown. Check your GUILD_ID in .env');
    }
    process.exit(1); 
  }
})(); 