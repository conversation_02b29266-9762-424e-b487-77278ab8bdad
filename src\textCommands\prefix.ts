/**
 * Prefix Command
 * Allows server administrators to change the command prefix for the server
 */
import { EmbedBuilder } from 'discord.js';
import { Message } from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';

export const name = 'prefix';
export const description = "View or change the bot's command prefix for this server";
export const usage = 'prefix [set <new_prefix>]';
export const devOnly = false;
export const adminOnly = true;

export const execute = async (message: Message, args: string[]) => {
  try {
    const currentPrefix = await dataManager.getGuildPrefix(message.guild.id);

    if (!args.length || args[0].toLowerCase() !== 'set') {
      return message.reply({
        content: `Current prefix: \`\`\`${currentPrefix}\`\`\`\nUse \`\`\`${currentPrefix}prefix set <new_prefix>\`\`\` to change it.`,
        allowedMentions: { repliedUser: false },
      });
    }

    if (!args[1]) {
      return message.reply({
        content: `Please specify a new prefix after 'set'.\nExample: \`\`\`${currentPrefix}prefix set !\`\`\``,
        allowedMentions: { repliedUser: false },
      });
    }

    const newPrefix = args[1];

    if (newPrefix.length > 5) {
      return message.reply({
        content: 'Prefix cannot be longer than 5 characters.',
        allowedMentions: { repliedUser: false },
      });
    }

    if (newPrefix === '`') {
      return message.reply({
        content: 'The backtick character (`) cannot be used as a prefix.',
        allowedMentions: { repliedUser: false },
      });
    }

    const success = await dataManager.setGuildPrefix(message.guild.id, newPrefix);

    if (success) {
      const embed = new EmbedBuilder()
        .setColor(0x00ff00)
        .setTitle('Prefix Updated')
        .setDescription(
          `The command prefix for this server has been changed to: \`\`\`${newPrefix}\`\`\``
        )
        .setFooter({
          text: `Changed by ${message.author.tag}`,
          iconURL: message.author.displayAvatarURL(),
        })
        .setTimestamp();

      await message.reply({ embeds: [embed], allowedMentions: { repliedUser: false } });
    } else {
      throw new Error('Failed to save new prefix');
    }
  } catch (error) {
    logger.error('Error in prefix command:', error);
    message
      .reply({
        content: 'An error occurred while updating the prefix.',
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
