/**
 * Guild Delete Event
 *
 * Triggered when the bot is removed from a server (kicked, banned, or left).
 * Logs detailed information about the guild and the event.
 */
import { Events } from 'discord.js';
import logger from '../utils/logger';
import guildLogger from '../utils/guildLogger';
import dataManager from '../utils/dataManager';

export const name = Events.GuildDelete;
export const once = false;
export const execute = async (guild, client) => {
  try {
    let leaveReason = 'unknown';

    if (guild.available === false) {
      leaveReason = 'guild_unavailable';
      logger.warn(`Guild ${guild.id} became unavailable, might be a Discord outage`);
      return;
    }

    const blacklistInfo = await dataManager.isServerBlacklisted(guild.id);
    if (blacklistInfo) {
      leaveReason = `blacklisted:${blacklistInfo.reason}`;
    } else {
      leaveReason = 'kicked_or_banned';
    }

    guildLogger.logGuildLeave(guild, leaveReason);

    try {
      const tempChannelsToRemove = [];

      for (const [channelId, userId] of client.tempChannels.entries()) {
        const channel = client.channels.cache.get(channelId);
        if (channel && channel.guild && channel.guild.id === guild.id) {
          tempChannelsToRemove.push(channelId);
        }
      }

      tempChannelsToRemove.forEach(channelId => {
        client.tempChannels.delete(channelId);
      });

      if (tempChannelsToRemove.length > 0) {
        dataManager.saveTempChannels(client.tempChannels);
        logger.info(
          `Removed ${tempChannelsToRemove.length} temporary channels for guild ${guild.id}`
        );
      }
    } catch (cleanupError) {
      logger.error(`Error cleaning up data for guild ${guild.id}:`, cleanupError);
    }
  } catch (error) {
    logger.error(`Error handling guild leave for ${guild.id}:`, error);
  }
};
