import { ActionRowBuilder, UserSelectMenuBuilder } from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'block';

export const execute = async (interaction, client, userChannel) => {
  try {
    const blockSelect = new UserSelectMenuBuilder()
      .setCustomId(`block_select_${userChannel.id}`)
      .setPlaceholder('Select user(s) to block')
      .setMinValues(1)
      .setMaxValues(10);

    const row: any = new ActionRowBuilder().addComponents(blockSelect);

    await replyOrFollowUpEphemeral(interaction, {
      content: 'Select user(s) to block from accessing your channel:',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending block select menu: ${error.message}`);

    await handleInteractionError('block.execute', error, interaction, client);
  }
};

export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `block.handleSelectMenu:selectMenu:${interaction.customId}`;

  if (!interaction.isUserSelectMenu()) return;

  try {
    const selectedUsers = interaction.users;
    const ownerId = interaction.user.id;
    const blockedUserTags: string[] = [];
    let errorsOccurred = false;

    logger.debug(
      `${menuSource}: User ${ownerId} selected ${selectedUsers.size} user(s) to block from channel ${targetChannel.id}`
    );

    const settings = (await dataManager.getUserSettings(ownerId)) || { blockedUsers: [] };
    settings.blockedUsers = settings.blockedUsers || [];

    for (const [selectedUserId, selectedUser] of selectedUsers) {
      if (selectedUserId === ownerId) {
        logger.warn(`${menuSource}: Owner ${ownerId} attempted to block themselves.`);
        await replyOrFollowUpEphemeral(interaction, {
          content: `${EMOJI[client.user.id].CROSS} You cannot block yourself.`,
        });
        return;
      }

      try {
        if (!settings.blockedUsers.includes(selectedUserId)) {
          settings.blockedUsers.push(selectedUserId);
        }

        await targetChannel.permissionOverwrites.edit(
          selectedUserId,
          {
            ViewChannel: false,
            Connect: false,
            Speak: false,
          },
          `Blocked by channel owner ${interaction.user.tag}`
        );

        const member = await interaction.guild.members.fetch(selectedUserId).catch(() => null);
        if (member && member.voice.channelId === targetChannel.id) {
          await member.voice
            .disconnect(`Blocked by channel owner ${interaction.user.tag}`)
            .catch(disconnectError => {
              logger.warn(
                `${menuSource}: Failed to disconnect blocked user ${selectedUserId}: ${disconnectError.message}`
              );
            });
        }
        blockedUserTags.push(selectedUser.tag);
        logger.info(
          `User ${selectedUser.tag} (${selectedUserId}) blocked from channel ${targetChannel.id} by owner ${ownerId}`
        );
      } catch (userBlockError) {
        logger.error(
          `${menuSource}: Failed to block user ${selectedUserId} (${selectedUser.tag}): ${userBlockError.message}`
        );
        errorsOccurred = true;
      }
    }

    logger.debug(`Updated blocked users settings for owner ${ownerId}`);

    let replyMessage = '';
    if (blockedUserTags.length > 0) {
      replyMessage += `${EMOJI[client.user.id].CHECK} Blocked ${blockedUserTags.join(', ')}.`;
    }
    if (errorsOccurred) {
      replyMessage +=
        (replyMessage ? '\n' : '') +
        '⚠️ Errors occurred while blocking some users. Check bot permissions.';
    }
    if (!replyMessage) {
      replyMessage = 'No users were blocked (possibly due to errors or selecting yourself).';
    }

    await replyOrFollowUpEphemeral(interaction, {
      content: replyMessage,
      components: [],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle block select menu: ${error.message}`);

    await handleInteractionError(menuSource, error, interaction, client);
  }
};
