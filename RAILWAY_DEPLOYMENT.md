# Railway Deployment Guide

This guide will help you deploy the Discord bot to Railway with optimized memory settings to prevent heap allocation errors.

## 🚀 Quick Deploy

[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/template/your-template-id)

## 📋 Prerequisites

1. **Railway Account**: Sign up at [railway.app](https://railway.app)
2. **Discord Bot Token**: Create a bot at [Discord Developer Portal](https://discord.com/developers/applications)
3. **MongoDB Database**: Get a free cluster at [MongoDB Atlas](https://www.mongodb.com/atlas)

## 🔧 Environment Variables

Set these environment variables in your Railway project:

### Required Variables
```env
TOKEN=your_discord_bot_token_here
CLIENT_ID=your_discord_client_id_here
MONGODB_URI=your_mongodb_connection_string
```

### Memory Optimization (Already configured)
```env
NODE_ENV=production
NODE_OPTIONS=--max-old-space-size=512 --optimize-for-size --gc-interval=100
SHARD_COUNT=1
SHARDED=false
```

### Optional Variables
```env
CONSOLE_LOG_LEVEL=INFO
ERROR_WEBHOOK_URL=your_error_webhook_url
GUILD_WEBHOOK_URL=your_guild_webhook_url
```

## 🛠️ Deployment Steps

### Method 1: GitHub Integration (Recommended)

1. **Fork this repository** to your GitHub account
2. **Connect to Railway**:
   - Go to [Railway Dashboard](https://railway.app/dashboard)
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Choose your forked repository

3. **Configure Environment Variables**:
   - Go to your project settings
   - Add all required environment variables listed above

4. **Deploy**:
   - Railway will automatically build and deploy your bot
   - Check the logs for any issues

### Method 2: Railway CLI

1. **Install Railway CLI**:
   ```bash
   npm install -g @railway/cli
   ```

2. **Login and Deploy**:
   ```bash
   railway login
   railway init
   railway up
   ```

## 🔍 Memory Optimization Features

This deployment includes several memory optimizations to prevent the "JavaScript heap out of memory" error:

### Node.js Flags
- `--max-old-space-size=512`: Limits heap to 512MB
- `--optimize-for-size`: Optimizes for memory usage over speed
- `--gc-interval=100`: More frequent garbage collection

### Application Optimizations
- **Reduced cache sizes**: Cache limited to 500 entries with 5s TTL
- **Optimized intervals**: Longer intervals for cleanup tasks
- **Memory monitoring**: Automatic garbage collection when memory usage is high
- **Cache clearing**: Automatic cache clearing when memory exceeds thresholds

## 📊 Monitoring

### Health Check
The bot includes a health check endpoint at `/health` that provides:
- Bot status and uptime
- Memory usage statistics
- Guild and channel counts
- Database connection status

### Logs
Monitor your bot through Railway's log viewer:
- Memory usage warnings
- Garbage collection events
- Performance metrics

## 🐛 Troubleshooting

### Memory Issues
If you still encounter memory issues:

1. **Check logs** for memory warnings
2. **Reduce bot activity** by increasing intervals in `src/constants/index.ts`
3. **Enable garbage collection** by setting `NODE_OPTIONS` with `--expose-gc`

### Build Issues
If the build fails:

1. **Check Node.js version** (should be 18+ for Railway)
2. **Verify dependencies** are properly installed
3. **Check TypeScript compilation** errors

### Runtime Issues
If the bot crashes:

1. **Check environment variables** are set correctly
2. **Verify MongoDB connection** string
3. **Check Discord token** permissions

## 📈 Performance Tips

1. **Monitor memory usage** through the health endpoint
2. **Adjust cache settings** if needed in `src/utils/dataManager.ts`
3. **Use sharding** only if you have 2000+ guilds
4. **Regular restarts** can help with memory management

## 🔗 Useful Links

- [Railway Documentation](https://docs.railway.app/)
- [Discord.js Guide](https://discordjs.guide/)
- [MongoDB Atlas Setup](https://docs.atlas.mongodb.com/getting-started/)

## 📞 Support

If you encounter issues:
1. Check the logs in Railway dashboard
2. Review this troubleshooting guide
3. Open an issue on GitHub with logs and error details
