/**
 * MyVC Discord Bot
 * Optimized for 100+ concurrent users
 */
require('dotenv').config();
import { Client, Collection, Events, GatewayIntentBits, Partials } from 'discord.js';
import fs from 'fs';
import path from 'path';
import http from 'http';

import logger from './utils/logger';
import { ExtendedClient } from './types';
import dataManager from './utils/dataManager';
import { createEmbed, formatBytes, formatUptime } from './utils/helpers';

const isSharded = process.env.SHARDS !== undefined || Boolean(process.env.SHARDED);

import { INTERVALS } from './constants';

let totalSaveOperations = 0;
let successfulSaveOperations = 0;
let totalMessagesProcessed = 0;
let totalCommandsUsed = 0;
const startTime = Date.now();

export const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.GuildVoiceStates,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.DirectMessages,
  ],
  partials: [Partials.Channel, Partials.Message],

  sweepers: {
    messages: {
      interval: 60,
      lifetime: 3600,
    },
  },
}) as ExtendedClient;

client.commands = new Collection();
client.textCommands = new Collection();
client.tempChannels = new Map();
client.userSettings = new Map();
client.defaultSettings = new Map();
client.cooldowns = new Collection();

const emptyWaitingRooms = new Map();

const saveQueue = new Set();
let savingInProgress = false;
let lastSaveAttempt = 0;
const SAVE_RETRY_INTERVAL = 30000;

/**
 * Queue user settings for saving
 * This batches saves to avoid excessive disk I/O
 * @param {string} userId - Optional user ID to prioritize
 * @param {boolean} immediate - Whether to try saving immediately
 */
function queueUserSettingsSave(userId = null, immediate = false) {
  if (userId) {
    saveQueue.add(userId);
  }

  const now = Date.now();
  const timeSinceLastSave = now - lastSaveAttempt;

  if (immediate || timeSinceLastSave >= SAVE_RETRY_INTERVAL) {
    if (!savingInProgress) {
      processSaveQueue();
    }
  }
}

/**
 * Process the user settings save queue
 * Limits writes to disk to avoid I/O bottlenecks
 */
async function processSaveQueue() {
  if (savingInProgress || saveQueue.size === 0) return;

  savingInProgress = true;
  lastSaveAttempt = Date.now();

  try {
    if (saveQueue.size > 0) {
      const success = await saveAllUserSettings();

      if (success) {
        saveQueue.clear();
        logger.info('Successfully processed save queue');
      } else {
        logger.warn('Failed to process save queue, will retry later');

        setTimeout(() => {
          savingInProgress = false;
          processSaveQueue();
        }, SAVE_RETRY_INTERVAL);
        return;
      }
    }
  } catch (error) {
    logger.error('Failed to process save queue:', error);

    setTimeout(() => {
      savingInProgress = false;
      processSaveQueue();
    }, SAVE_RETRY_INTERVAL);
    return;
  } finally {
    savingInProgress = false;
  }

  if (saveQueue.size > 0) {
    setTimeout(processSaveQueue, 1000);
  }
}

/**
 * Save all user settings to file
 * Converts Map to object for storage
 */
async function saveAllUserSettings() {
  totalSaveOperations++;

  try {
    for (const [userId, settings] of client.userSettings) {
      if (!settings || typeof settings !== 'object') {
        logger.warn(`Invalid settings detected for user ${userId}, skipping`);
        client.userSettings.delete(userId);
        continue;
      }
    }

    const settingsObj = {};
    for (const [userId, settings] of client.userSettings) {
      const settingsCopy = JSON.parse(JSON.stringify(settings));

      for (const key in settingsCopy) {
        if (settingsCopy[key] === undefined) {
          delete settingsCopy[key];
        }
      }

      settingsCopy.lastUpdated = new Date();

      settingsObj[userId] = settingsCopy;
    }

    const success = await dataManager.saveUserSettings(settingsObj);

    if (success) {
      successfulSaveOperations++;
      logger.info(
        `Successfully saved settings for ${Object.keys(settingsObj).length} users to MongoDB`
      );

      try {
        const savedData = await dataManager.loadUserSettings();
        const savedCount = Object.keys(savedData).length;

        if (Object.keys(savedData).length > 0) {
          const sampleUserId = Object.keys(savedData)[0];
          const sampleData = savedData[sampleUserId];
          logger.debug(
            `Sample saved user data for ${sampleUserId}: ${JSON.stringify({
              name: sampleData.name || 'N/A',
              isPrivate: sampleData.isPrivate || false,
              hasWaitingRoom: sampleData.hasWaitingRoom || false,
              userLimit: sampleData.userLimit || 0,
              permissionOverwrites: sampleData.permissionOverwrites
                ? sampleData.permissionOverwrites.length
                : 0,
            })}`
          );
        }

        if (savedCount !== Object.keys(settingsObj).length) {
          logger.warn(
            `Data verification mismatch: saved ${Object.keys(settingsObj).length} users but read back ${savedCount}`
          );
          return false;
        }
      } catch (error) {
        logger.error('Failed to verify saved data:', error);
        return false;
      }
    } else {
      logger.error('Failed to save user settings to MongoDB');
      return false;
    }

    return true;
  } catch (error) {
    logger.error('Failed to save user settings:', error);
    return false;
  }
}

/**
 * Make saveUserSettings function globally available
 * @param {string} userId - Optional user ID to prioritize
 */
global.saveUserSettings = queueUserSettingsSave;

/**
 * Load user settings from MongoDB
 */
async function loadUserSettings() {
  try {
    const settingsObj = await dataManager.loadUserSettings();

    client.userSettings.clear();

    for (const [userId, settings] of Object.entries(settingsObj)) {
      if (settings) {
        client.userSettings.set(userId, settings);
      }
    }

    logger.info(`Loaded settings for ${client.userSettings.size} users from MongoDB`);

    if (client.userSettings.size > 0) {
      const sampleUserId = Array.from(client.userSettings.keys())[0];
      const sampleData = client.userSettings.get(sampleUserId);
      logger.debug(
        `Sample loaded user data for ${sampleUserId}: ${JSON.stringify({
          name: sampleData.name || 'N/A',
          isPrivate: sampleData.isPrivate || false,
          hasWaitingRoom: sampleData.hasWaitingRoom || false,
          userLimit: sampleData.userLimit || 0,
          permissionOverwrites: sampleData.permissionOverwrites
            ? sampleData.permissionOverwrites.length
            : 0,
        })}`
      );
    }
  } catch (error) {
    logger.error('Failed to load user settings from MongoDB:', error);
  }
}

/**
 * Saves bot performance stats to MongoDB
 */
async function saveBotStats() {
  try {
    const memoryUsage = process.memoryUsage();
    const stats = {
      memory: {
        rss: formatBytes(memoryUsage.rss),
        heapTotal: formatBytes(memoryUsage.heapTotal),
        heapUsed: formatBytes(memoryUsage.heapUsed),
        external: formatBytes(memoryUsage.external),
      },
      timestamp: new Date().toISOString(),
    };

    try {
      await dataManager.saveBotStats(stats);
      logger.info('Successfully saved bot stats to MongoDB');
    } catch (error) {
      logger.error('Failed to save bot stats to MongoDB:', error);
    }
  } catch (error) {
    logger.error('Failed to collect bot stats:', error);
  }
}

/**
 * Clean up empty voice channels to avoid memory leaks
 * Also deletes empty channels if specified
 * @param {boolean} deleteEmptyChannels - Whether to delete empty channels
 */
async function cleanupEmptyVoiceChannels(deleteEmptyChannels = false) {
  try {
    let channelsRemoved = 0;
    let channelsDeleted = 0;
    let channelsChecked = 0;
    let waitingRoomsDeleted = 0;
    const deletionPromises = [];

    if (client.tempChannels.size === 0) {
      logger.debug('No temporary channels to check, skipping cleanup');

      if (client.userSettings.size > 0) {
        logger.debug(
          `Checking ${client.userSettings.size} user settings for orphaned waiting rooms`
        );
        await cleanupOrphanedWaitingRooms();
      }

      return;
    }

    logger.debug(`Starting cleanup process, checking ${client.tempChannels.size} tracked channels`);

    const guildSettingsPromise = dataManager.loadGuildSettings();

    const usersWithChannels = new Set();

    const guildChannelsMap = new Map();

    for (const guild of client.guilds.cache.values()) {
      guildChannelsMap.set(guild.id, guild.channels.cache);
    }

    await checkEmptyWaitingRooms();

    const channelEntries = Array.from(client.tempChannels.entries());
    const batchSize = 10;

    for (let i = 0; i < channelEntries.length; i += batchSize) {
      const batch = channelEntries.slice(i, i + batchSize);

      await Promise.all(
        batch.map(async ([channelId, ownerId]) => {
          channelsChecked++;

          let channel = null;
          let foundGuild = null;

          for (const [guildId, channelsCache] of guildChannelsMap.entries()) {
            const ch = channelsCache.get(channelId);
            if (ch) {
              channel = ch;
              foundGuild = client.guilds.cache.get(guildId);
              break;
            }
          }

          if (!channel) {
            client.tempChannels.delete(channelId);
            channelsRemoved++;
            logger.info(
              `Removed stale channel ${channelId} from tracking (doesn't exist in any guild)`
            );
            return;
          }

          usersWithChannels.add(ownerId);

          if (channel.type === 2 && channel.members.size === 0) {
            const isWaitingRoom = channel.name.toLowerCase().includes('waiting-room');

            if (isWaitingRoom) {
              if (!emptyWaitingRooms.has(channelId)) {
                logger.debug(
                  `Found empty waiting room ${channel.name} (${channelId}), tracking for 1-minute deletion`
                );
                emptyWaitingRooms.set(channelId, Date.now());
              }
              return;
            }

            client.tempChannels.delete(channelId);
            channelsRemoved++;

            if (deleteEmptyChannels) {
              try {
                const guildSettings = await dataManager.loadGuildSettings();
                const guildId = channel.guild.id;
                const settings = guildSettings[guildId] || {};
                const joinChannelId = settings.joinChannelId || settings.JoinChannel;

                const isJoinChannel = channelId === joinChannelId;
                const hasJoinInName = channel.name.toLowerCase().includes('join');

                if (isJoinChannel) {
                  logger.debug(
                    `Regular cleanup: Skipping JOIN_TO_CREATE channel ${channel.name} (${channelId})`
                  );
                } else if (hasJoinInName) {
                  logger.debug(
                    `Regular cleanup: Skipping channel with 'join' in name: ${channel.name} (${channelId})`
                  );
                } else {
                  logger.info(`Deleting empty channel ${channel.name} (${channelId})`);

                  deletionPromises.push(
                    channel
                      .delete(`Automatic cleanup: Channel empty`)
                      .then(() => {
                        channelsDeleted++;
                        logger.info(`Successfully deleted empty channel ${channelId}`);
                      })
                      .catch(error => {
                        logger.error(`Failed to delete channel ${channelId}: ${error.message}`);
                      })
                  );
                }
              } catch (error) {
                logger.error(`Error during channel deletion: ${error.message}`);
              }
            } else {
              logger.info(`Removed stale channel ${channelId} from tracking (empty)`);
            }
          } else if (channel.type === 2) {
            let ownerConnectedAnywhere = false;

            for (const guild of client.guilds.cache.values()) {
              try {
                const guildMember = await guild.members
                  .fetch({ user: ownerId.toString() })
                  .catch(() => null);
                if (guildMember && guildMember.voice.channelId) {
                  ownerConnectedAnywhere = true;
                  break;
                }
              } catch (error) {
                continue;
              }
            }

            if (!ownerConnectedAnywhere && channel.members.size > 0) {
              logger.info(
                `Owner ${ownerId} not connected to any voice channel but channel ${channelId} still has members. Keeping it tracked.`
              );
            }
          }
        })
      );
    }

    if (deletionPromises.length > 0) {
      await Promise.allSettled(deletionPromises);
    }

    if (client.userSettings.size > 0) {
      waitingRoomsDeleted = await cleanupOrphanedWaitingRooms(usersWithChannels);
    }

    if (channelsRemoved > 0) {
      saveTempChannels().catch(err => {
        logger.error(`Failed to save temp channels during cleanup: ${err.message}`);
      });

      logger.info(
        `Cleanup complete: Checked ${channelsChecked} channels, removed ${channelsRemoved} stale channels, deleted ${channelsDeleted} empty channels, deleted ${waitingRoomsDeleted} orphaned waiting rooms`
      );
    } else {
      logger.debug(
        `Cleanup complete: No channels needed removal, deleted ${waitingRoomsDeleted} orphaned waiting rooms`
      );
    }
  } catch (error) {
    logger.error(`Failed to clean up empty voice channels: ${error.message}`);
  }
}

/**
 * Check for empty waiting rooms and track them for deletion after 1 minute
 * @returns {number} - Number of waiting rooms deleted
 */
async function checkEmptyWaitingRooms() {
  let waitingRoomsDeleted = 0;
  const deletionPromises = [];
  const now = Date.now();

  try {
    for (const [waitingRoomId, timestamp] of emptyWaitingRooms.entries()) {
      if (now - timestamp >= 60000) {
        logger.info(`Waiting room ${waitingRoomId} has been empty for 1 minute, deleting it`);

        let waitingRoomChannel = null;
        let foundGuild = null;

        for (const guild of client.guilds.cache.values()) {
          try {
            waitingRoomChannel = await guild.channels.fetch(waitingRoomId).catch(() => null);
            if (waitingRoomChannel) {
              foundGuild = guild;
              break;
            }
          } catch (error) {
            continue;
          }
        }

        if (waitingRoomChannel) {
          let ownerId = null;
          for (const [userId, settings] of client.userSettings.entries()) {
            if (settings.waitingRoomId === waitingRoomId) {
              ownerId = userId;
              break;
            }
          }

          deletionPromises.push(
            waitingRoomChannel
              .delete(`Automatic cleanup: Waiting room empty for 1 minute`)
              .then(() => {
                waitingRoomsDeleted++;
                logger.info(`Successfully deleted empty waiting room ${waitingRoomId}`);

                if (ownerId) {
                  const settings = client.userSettings.get(ownerId);
                  if (settings) {
                    settings.hasWaitingRoom = false;
                    settings.waitingRoomId = null;
                    client.userSettings.set(ownerId, settings);
                    queueUserSettingsSave(ownerId);
                    logger.debug(
                      `Updated user settings for ${ownerId}: waiting room flags cleared`
                    );
                  }
                }

                return true;
              })
              .catch(error => {
                logger.error(`Failed to delete waiting room ${waitingRoomId}: ${error.message}`);
                return false;
              })
          );
        }

        emptyWaitingRooms.delete(waitingRoomId);
      }
    }

    if (deletionPromises.length > 0) {
      await Promise.allSettled(deletionPromises);
    }

    return waitingRoomsDeleted;
  } catch (error) {
    logger.error(`Error checking empty waiting rooms: ${error.message}`);
    return 0;
  }
}

/**
 * Clean up orphaned waiting rooms
 * @param {Set} usersWithChannels - Set of user IDs who still have valid channels
 * @returns {number} - Number of waiting rooms deleted
 */
async function cleanupOrphanedWaitingRooms(usersWithChannels = new Set()) {
  let waitingRoomsDeleted = 0;
  const deletionPromises = [];
  const settingsToUpdate = [];

  try {
    logger.debug(`Checking for orphaned waiting rooms...`);

    const allChannelsMap = new Map();

    for (const guild of client.guilds.cache.values()) {
      for (const [channelId, channel] of guild.channels.cache.entries()) {
        allChannelsMap.set(channelId, { channel, guild });
      }
    }

    const orphanedWaitingRooms = [];

    for (const [userId, settings] of client.userSettings.entries()) {
      if (usersWithChannels.has(userId)) {
        continue;
      }

      if (settings.hasWaitingRoom && settings.waitingRoomId) {
        const waitingRoomInfo = allChannelsMap.get(settings.waitingRoomId);

        if (waitingRoomInfo) {
          orphanedWaitingRooms.push({
            userId,
            settings,
            waitingRoom: waitingRoomInfo.channel,
            waitingRoomId: settings.waitingRoomId,
          });
        } else {
          settings.hasWaitingRoom = false;
          settings.waitingRoomId = null;
          settingsToUpdate.push(userId);
          client.userSettings.set(userId, settings);
          logger.debug(
            `User settings updated: waiting room flags cleared for user ${userId} (room not found)`
          );
        }
      }
    }

    if (orphanedWaitingRooms.length > 0) {
      logger.info(`Found ${orphanedWaitingRooms.length} orphaned waiting rooms to delete`);

      const batchSize = 5;
      for (let i = 0; i < orphanedWaitingRooms.length; i += batchSize) {
        const batch = orphanedWaitingRooms.slice(i, i + batchSize);

        const batchPromises = batch.map(({ userId, settings, waitingRoom, waitingRoomId }) => {
          logger.info(
            `Deleting orphaned waiting room ${waitingRoom.name} (${waitingRoomId}) for user ${userId}`
          );

          settings.hasWaitingRoom = false;
          settings.waitingRoomId = null;
          client.userSettings.set(userId, settings);
          settingsToUpdate.push(userId);

          return waitingRoom
            .delete(`Automatic cleanup: Orphaned waiting room`)
            .then(() => {
              waitingRoomsDeleted++;
              logger.info(`Successfully deleted orphaned waiting room ${waitingRoomId}`);
              return true;
            })
            .catch(error => {
              logger.error(`Failed to delete waiting room ${waitingRoomId}: ${error.message}`);
              return false;
            });
        });

        await Promise.allSettled(batchPromises);
      }
    }

    if (settingsToUpdate.length > 0) {
      const settingsObj = {};
      for (const userId of settingsToUpdate) {
        settingsObj[userId] = client.userSettings.get(userId);
      }

      dataManager.saveUserSettings(settingsObj).catch(error => {
        logger.error(`Failed to save updated user settings: ${error.message}`);

        for (const userId of settingsToUpdate) {
          global.saveUserSettings(userId);
        }
      });
    }

    if (waitingRoomsDeleted > 0) {
      logger.info(`Cleaned up ${waitingRoomsDeleted} orphaned waiting rooms`);
    } else {
      logger.debug(`No orphaned waiting rooms found`);
    }

    return waitingRoomsDeleted;
  } catch (error) {
    logger.error(`Failed to clean up orphaned waiting rooms: ${error.message}`);
    return 0;
  }
}

/**
 * Specifically checks MyVC categories for empty channels and deletes them immediately
 * This is a more aggressive cleanup that runs every 20 seconds
 * Also checks for empty waiting rooms and tracks them for deletion after 1 minute
 */
async function cleanupEmptyVoiceChannelsInCategories() {
  try {
    let channelsDeleted = 0;
    const deletionPromises = [];

    await checkEmptyWaitingRooms();

    const guildSettings = await dataManager.loadGuildSettings();

    for (const guild of client.guilds.cache.values()) {
      try {
        const guildId = guild.id;
        const settings = guildSettings[guildId] || {};
        const tempCategoryId = settings.tempCategoryId || settings.TempCategory;

        if (!tempCategoryId) {
          continue;
        }

        const guildChannels = guild.channels.cache;

        for (const [channelId, channel] of guildChannels.entries()) {
          if (channel.type !== 2) {
            continue;
          }

          const isWaitingRoom = channel.name.toLowerCase().includes('waiting-room');

          if (isWaitingRoom && channel.members.size === 0) {
            if (!emptyWaitingRooms.has(channelId)) {
              logger.debug(
                `Found empty waiting room ${channel.name} (${channelId}), tracking for 1-minute deletion`
              );
              emptyWaitingRooms.set(channelId, Date.now());
            }

            continue;
          }

          if (channel.parentId === tempCategoryId) {
            const joinChannelId = settings.joinChannelId || settings.JoinChannel;

            const isJoinChannel = channelId === joinChannelId;
            const hasJoinInName = channel.name.toLowerCase().includes('join');

            if (isJoinChannel) {
              logger.debug(
                `Category cleanup: Skipping JOIN_TO_CREATE channel ${channel.name} (${channelId})`
              );
            } else if (hasJoinInName) {
              logger.debug(
                `Category cleanup: Skipping channel with 'join' in name: ${channel.name} (${channelId})`
              );
            } else if (channel.members.size === 0) {
              logger.info(
                `Category cleanup: Deleting empty channel ${channel.name} (${channelId})`
              );

              client.tempChannels.delete(channelId);

              deletionPromises.push(
                channel
                  .delete(`Automatic category cleanup: Channel empty`)
                  .then(() => {
                    channelsDeleted++;
                    logger.info(
                      `Category cleanup: Successfully deleted empty channel ${channelId}`
                    );
                  })
                  .catch(error => {
                    logger.error(
                      `Category cleanup: Failed to delete channel ${channelId}: ${error.message}`
                    );
                  })
              );
            }
          }
        }
      } catch (error) {
        logger.error(`Error cleaning up guild ${guild.id}: ${error.message}`);
      }
    }

    await Promise.allSettled(deletionPromises);

    if (channelsDeleted > 0) {
      logger.info(`Category cleanup complete: Deleted ${channelsDeleted} empty channels`);

      saveTempChannels().catch(err => {
        logger.error(`Failed to save temp channels during category cleanup: ${err.message}`);
      });
    }

    return channelsDeleted;
  } catch (error) {
    logger.error(`Error in category cleanup: ${error.message}`);
    return 0;
  }
}

/**
 * Clean up voice channels on bot startup
 * This ensures any empty channels left when the bot was offline are properly cleaned up
 */
async function startupCleanup() {
  logger.info('Starting initial cleanup of voice channels...');

  try {
    logger.debug('Fetching only MyVC category channels from Discord API...');

    let fetchedChannels = 0;
    const fetchPromises = [];

    const guildSettings = await dataManager.loadGuildSettings();

    for (const guild of client.guilds.cache.values()) {
      if (guildSettings[guild.id] && guildSettings[guild.id].tempCategoryId) {
        const categoryId = guildSettings[guild.id].tempCategoryId;

        fetchPromises.push(
          guild.channels
            .fetch(categoryId)
            .then(async category => {
              if (category) {
                const childChannels = await guild.channels
                  .fetch()
                  .then(channels => channels.filter(c => c.parentId === categoryId));

                fetchedChannels += childChannels.size;
                logger.debug(
                  `Fetched ${childChannels.size} channels from MyVC category in guild ${guild.name} (${guild.id})`
                );
              } else {
                logger.warn(
                  `MyVC category ${categoryId} not found in guild ${guild.name} (${guild.id})`
                );
              }
            })
            .catch(async error => {
              if (error.message === 'Unknown Channel') {
                logger.warn(
                  `MyVC category ${categoryId} no longer exists in guild ${guild.name} (${guild.id}). Cleaning up guild settings.`
                );

                try {
                  await dataManager.updateGuildSettings(guild.id, {
                    tempCategoryId: null,
                    joinChannelId: null,
                  });
                  logger.info(`Cleaned up invalid category ID for guild ${guild.id}`);
                } catch (cleanupError) {
                  logger.error(`Failed to clean up guild settings for ${guild.id}:`, cleanupError);
                }
              } else {
                logger.error(
                  `Failed to fetch MyVC category for guild ${guild.id}: ${error.message}`
                );
              }
            })
        );
      } else {
        logger.debug(`Guild ${guild.name} (${guild.id}) has no MyVC category configured, skipping`);
      }
    }

    await Promise.all(fetchPromises);
    logger.info(
      `Fetched a total of ${fetchedChannels} channels from MyVC categories across ${client.guilds.cache.size} guilds`
    );

    await cleanupEmptyVoiceChannels(true);
  } catch (error) {
    logger.error(`Error during startup cleanup: ${error.message}`);

    await cleanupEmptyVoiceChannels(true);
  }
}

/**
 * Load temporary channels from file
 */
async function loadTempChannels() {
  try {
    logger.debug('Cleaning up stale channels from database before loading...');
    const removedCount = await dataManager.cleanupStaleTempChannels();
    if (removedCount > 0) {
      logger.info(`Removed ${removedCount} stale channels from database during startup`);
    }

    const channelsObj = await dataManager.loadTempChannels();

    let loadedCount = 0;
    for (const [channelId, userId] of Object.entries(channelsObj) as any) {
      client.tempChannels.set(channelId, userId);
      loadedCount++;
    }

    logger.info(`Loaded ${loadedCount} temporary channels from storage`);
  } catch (error) {
    logger.error('Failed to load temporary channels:', error);
  }
}

/**
 * Save temporary channels to file
 */
async function saveTempChannels() {
  try {
    const success = await dataManager.saveTempChannels(client.tempChannels);

    if (success) {
      logger.debug(`Successfully saved ${client.tempChannels.size} temp channels`);
    } else {
      logger.error('Failed to save temp channels');
    }

    return success;
  } catch (error) {
    logger.error('Failed to save temp channels:', error);
    return false;
  }
}

global.createEmbed = createEmbed;

global.incrementMessageCount = () => totalMessagesProcessed++;
global.incrementCommandCount = () => totalCommandsUsed++;
global.getStats = () => ({
  totalMessagesProcessed,
  totalCommandsUsed,
  totalSaveOperations,
  successfulSaveOperations,
  startTime,
});

const fileExtension = __filename.endsWith('.ts') ? '.ts' : '.js';

const commandsPath = path.join(__dirname, 'commands');

if (fs.existsSync(commandsPath)) {
  const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith(fileExtension));

  logger.info(`Found ${commandFiles.length} command files`);

  for (const file of commandFiles) {
    const filePath = path.join(commandsPath, file);
    try {
      const command = require(filePath);

      if ('data' in command && 'execute' in command) {
        client.commands.set(command.data.name, command);
        logger.debug(`Registered command: ${command.data.name}`);
      } else {
        logger.warn(
          `The command at ${filePath} is missing a required "data" or "execute" property.`
        );
      }
    } catch (error) {
      logger.error(`Error loading command from ${file}:`, error);
    }
  }
} else {
  logger.warn(`Commands directory not found: ${commandsPath}`);
}

const textCommandsPath = path.join(__dirname, 'textCommands');
if (fs.existsSync(textCommandsPath)) {
  const textCommandFiles = fs
    .readdirSync(textCommandsPath)
    .filter(file => file.endsWith(fileExtension));

  logger.info(`Found ${textCommandFiles.length} text command files`);

  for (const file of textCommandFiles) {
    const filePath = path.join(textCommandsPath, file);
    try {
      const command = require(filePath);

      if ('name' in command && 'execute' in command) {
        client.textCommands.set(command.name, command);
        logger.debug(`Registered text command: ${command.name}`);
      } else {
        logger.warn(
          `The text command at ${filePath} is missing a required "name" or "execute" property.`
        );
      }
    } catch (error) {
      logger.error(`Error loading text command from ${file}:`, error);
    }
  }
}

/**
 * Load a command into the bot
 * @param {string} commandName - Name of the command to load
 * @returns {Object} Result with success status and message
 */
function loadCommand(commandName) {
  try {
    const fileExt = __filename.endsWith('.ts') ? '.ts' : '.js';
    const commandPath = path.join(__dirname, 'textCommands', `${commandName}${fileExt}`);

    if (!fs.existsSync(commandPath)) {
      return { success: false, message: `Command file ${commandName}${fileExt} does not exist` };
    }

    delete require.cache[require.resolve(commandPath)];

    const command = require(commandPath);

    if (!command.name || !command.execute) {
      return { success: false, message: `Command ${commandName} is missing required properties` };
    }

    client.textCommands.set(command.name, command);

    return { success: true, message: `Command ${commandName} was loaded successfully` };
  } catch (error) {
    logger.error(`Error loading command ${commandName}:`, error);
    return { success: false, message: `Error loading command: ${error.message}` };
  }
}

/**
 * Unload a command from the bot
 * @param {string} commandName - Name of the command to unload
 * @returns {Object} Result with success status and message
 */
function unloadCommand(commandName) {
  try {
    if (!client.textCommands.has(commandName)) {
      return { success: false, message: `Command ${commandName} is not loaded` };
    }

    const fileExt = __filename.endsWith('.ts') ? '.ts' : '.js';
    const commandPath = path.join(__dirname, 'textCommands', `${commandName}${fileExt}`);

    delete require.cache[require.resolve(commandPath)];

    client.textCommands.delete(commandName);

    return { success: true, message: `Command ${commandName} was unloaded successfully` };
  } catch (error) {
    logger.error(`Error unloading command ${commandName}:`, error);
    return { success: false, message: `Error unloading command: ${error.message}` };
  }
}

/**
 * Reload a command
 * @param {string} commandName - Name of the command to reload
 * @returns {Object} Result with success status and message
 */
function reloadCommand(commandName) {
  const unloadResult = unloadCommand(commandName);

  if (!unloadResult.success) {
    return unloadResult;
  }

  return loadCommand(commandName);
}

global.loadCommand = loadCommand;
global.unloadCommand = unloadCommand;
global.reloadCommand = reloadCommand;
global.loadTempChannels = loadTempChannels;
global.saveTempChannels = saveTempChannels;
global.startupCleanup = startupCleanup;
global.cleanupEmptyVoiceChannelsInCategories = cleanupEmptyVoiceChannelsInCategories;
global.emptyWaitingRooms = emptyWaitingRooms;

const eventsPath = path.join(__dirname, 'events');

if (fs.existsSync(eventsPath)) {
  const eventFiles = fs.readdirSync(eventsPath).filter(file => file.endsWith(fileExtension));

  logger.info(`Found ${eventFiles.length} event files`);

  for (const file of eventFiles) {
    const filePath = path.join(eventsPath, file);
    try {
      const event = require(filePath);

      if (event.once) {
        client.once(event.name, (...args) => event.execute(...args, client));
        logger.debug(`Registered one-time event: ${event.name}`);
      } else {
        client.on(event.name, (...args) => event.execute(...args, client));
        logger.debug(`Registered event: ${event.name}`);
      }
    } catch (error) {
      logger.error(`Error loading event from ${file}:`, error);
    }
  }
} else {
  logger.warn(`Events directory not found: ${eventsPath}`);
}

let userSettingsSaveInterval;
let cleanupInterval;
let categoryCleanupInterval;
let statsInterval;

client.once(Events.ClientReady, async () => {
  if (client.shard) {
    logger.info(`Shard ${client.shard.ids.join('/')} logged in as ${client.user.tag}`);
    logger.info(`This shard is responsible for ${client.guilds.cache.size} guilds`);
  } else {
    logger.info(`Logged in as ${client.user.tag}`);
    logger.info(`Ready to serve in ${client.guilds.cache.size} guilds`);
  }

  await loadUserSettings();

  setTimeout(async () => {
    logger.debug('Cleaning up invalid guild settings...');
    const cleanedGuilds = await dataManager.cleanupInvalidGuildSettings();
    if (cleanedGuilds > 0) {
      logger.info(`Cleaned up invalid settings for ${cleanedGuilds} guilds during startup`);
    }

    await loadTempChannels();
    logger.info('Loaded temporary channels data');

    setTimeout(async () => {
      await startupCleanup();
      logger.info('Startup cleanup completed');
    }, 5000);
  }, 3000);

  userSettingsSaveInterval = setInterval(() => {
    if (client.userSettings.size > 0) {
      queueUserSettingsSave();
    }
  }, INTERVALS.SAVE_DATA);

  cleanupInterval = setInterval(async () => {
    await cleanupEmptyVoiceChannels(true);
  }, INTERVALS.CLEANUP);

  categoryCleanupInterval = setInterval(async () => {
    await cleanupEmptyVoiceChannelsInCategories();
  }, INTERVALS.CATEGORY_CLEANUP);

  statsInterval = setInterval(async () => {
    await saveBotStats();
  }, INTERVALS.STATS);

  // Memory monitoring and garbage collection
  setInterval(() => {
    const memoryUsage = process.memoryUsage();
    const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
    const heapTotalMB = memoryUsage.heapTotal / 1024 / 1024;

    // Log memory usage
    logger.debug(`Memory usage: ${heapUsedMB.toFixed(2)}MB / ${heapTotalMB.toFixed(2)}MB`);

    // Force garbage collection if memory usage is high (above 400MB)
    if (heapUsedMB > 400 && global.gc) {
      logger.warn(`High memory usage detected (${heapUsedMB.toFixed(2)}MB), forcing garbage collection`);
      global.gc();

      // Log memory after GC
      const afterGC = process.memoryUsage();
      const afterGCMB = afterGC.heapUsed / 1024 / 1024;
      logger.info(`Memory after GC: ${afterGCMB.toFixed(2)}MB (freed ${(heapUsedMB - afterGCMB).toFixed(2)}MB)`);
    }

    // Clear caches if memory is still high
    if (heapUsedMB > 450) {
      logger.warn('Memory usage still high, clearing caches');
      dataManager.clearCache();

      // Clear Discord.js caches if we have too many guilds cached
      if (client.guilds?.cache?.size > 100) {
        client.guilds.cache.clear();
      }
    }
  }, INTERVALS.MEMORY_CHECK);

  setInterval(() => {
    const uptime = Date.now() - startTime;
    const memoryUsage = process.memoryUsage();

    logger.info(`Bot uptime: ${formatUptime(uptime)}`);
    logger.info(
      `Memory usage: ${formatBytes(memoryUsage.rss)} (RSS), ${formatBytes(memoryUsage.heapUsed)} (Heap)`
    );
    logger.info(`Tracking ${client.tempChannels.size} temporary channels`);
    logger.info(`Storing settings for ${client.userSettings.size} users`);
    logger.info(
      `Messages processed: ${totalMessagesProcessed}, Commands used: ${totalCommandsUsed}`
    );
    logger.info(
      `Total save operations: ${totalSaveOperations}, successful: ${successfulSaveOperations}`
    );
  }, INTERVALS.PERFORMANCE_LOG);
});

process.on('SIGINT', handleShutdown);
process.on('SIGTERM', handleShutdown);

/**
 * Clean shutdown handler
 */
async function handleShutdown() {
  try {
    logger.info(
      `Shutting down${client.shard ? ` shard ${client.shard.ids.join('/')}` : ''}, cleaning up...`
    );

    clearInterval(userSettingsSaveInterval);
    clearInterval(cleanupInterval);
    clearInterval(categoryCleanupInterval);
    clearInterval(statsInterval);

    saveQueue.clear();

    if (client) {
      logger.info('Destroying Discord client...');
      await client.destroy();
      logger.info('Discord client destroyed');
    }

    logger.info('Running data manager cleanup...');
    await dataManager.cleanup();

    logger.info('Shutdown completed successfully');

    if (!client.shard) {
      process.exit(0);
    }
  } catch (error) {
    logger.error('Error during shutdown:', error);

    if (!client.shard) {
      process.exit(1);
    }
  }
}

// Create simple HTTP health check server
const createHealthCheckServer = () => {
  const server = http.createServer((req, res) => {
    if (req.url === '/health') {
      const healthData = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        bot: {
          ready: client.isReady(),
          guilds: client.guilds?.cache.size || 0,
          tempChannels: client.tempChannels?.size || 0,
          userSettings: client.userSettings?.size || 0,
        }
      };

      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(healthData, null, 2));
    } else {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Not Found');
    }
  });

  const port = process.env.PORT || 3000;
  server.listen(port, () => {
    logger.info(`Health check server listening on port ${port}`);
  });

  return server;
};

const startBot = async () => {
  try {
    logger.info('Connecting to database...');
    await dataManager.connectToMongoDB();
    logger.info('Database connection successful.');

    // Start health check server
    createHealthCheckServer();

    logger.info(`Logging in to Discord${isSharded ? ' (Shard Mode)' : ''}...`);
    await client.login(process.env.TOKEN);
  } catch (error) {
    logger.error('Failed to start bot:', error);
    process.exit(1);
  }
};

startBot();

process.on('uncaughtException', error => {
  logger.error('Uncaught Exception:', error);

  if (!(error.name === 'DiscordAPIError')) {
    handleShutdown();
  }
});

process.on('unhandledRejection', (reason: any, promise) => {
  logger.error('Unhandled Rejection at:', promise);
  logger.error('Rejection reason:', reason);

  if (reason && !(reason.name === 'DiscordAPIError')) {
    logger.error('Shutting down due to unhandled rejection...');
    handleShutdown();
  }
});

if (client.shard) {
  global.getShardInfo = () => {
    return {
      id: client.shard?.ids[0],
      guildCount: client.guilds.cache.size,
      tempChannelCount: client.tempChannels?.size || 0,
      userSettingsCount: client.userSettings?.size || 0,
      uptime: client.uptime,
      memoryUsage: process.memoryUsage(),
    };
  };
}
