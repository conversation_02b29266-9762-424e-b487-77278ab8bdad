/**
 * Blacklist Command
 * Allows bot developers to blacklist problematic servers
 */
import { EmbedBuilder } from 'discord.js';
import { Message, Client } from 'discord.js';
import { isDeveloper } from '../constants/devs';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { EMOJI } from '../constants/emoji';

interface BlacklistEntry {
  reason: string;
  blacklistedBy: string;
  blacklistedAt: string;
}

interface BlacklistData {
  blacklistedServers: { [key: string]: BlacklistEntry };
}

export const name = 'blacklist';
export const description = 'Blacklist a server from using the bot';
export const usage = 'blacklist <add/remove/list> [server_id] [reason]';
export const devOnly = true;
export const adminOnly = false;

export const execute = async (message: Message, args: string[], client: Client) => {
  if (!isDeveloper(message.author.id)) {
    return message.reply({
      content: 'You do not have permission to use this command.',
      allowedMentions: { repliedUser: false },
    });
  }

  try {
    if (!args[0]) {
      return message.reply({
        content: 'Please specify a subcommand: `add`, `remove`, or `list`',
        allowedMentions: { repliedUser: false },
      });
    }

    const subCommand = args[0].toLowerCase();

    const blacklist: BlacklistData = await dataManager.loadBlacklistedServers();

    switch (subCommand) {
      case 'add': {
        if (!args[1]) {
          return message.reply({
            content: 'Please specify a server ID to blacklist',
            allowedMentions: { repliedUser: false },
          });
        }

        const serverId = args[1];

        if (!/^\d{17,20}$/.test(serverId)) {
          return message.reply({
            content: 'Invalid server ID. Please provide a valid Discord server ID.',
            allowedMentions: { repliedUser: false },
          });
        }

        let reason = args.slice(2).join(' ') || 'No reason provided';
        if (reason.length > 256) reason = reason.slice(0, 256);

        if (blacklist.blacklistedServers[serverId]) {
          const entry = blacklist.blacklistedServers[serverId];
          return message.reply({
            content: `Server with ID \`${serverId}\` is already blacklisted.\n• Reason: ${entry.reason}\n• By: <@${entry.blacklistedBy}>\n• When: <t:${Math.floor(new Date(entry.blacklistedAt).getTime() / 1000)}:R>`,
            allowedMentions: { repliedUser: false },
          });
        }

        blacklist.blacklistedServers[serverId] = {
          reason: reason,
          blacklistedBy: message.author.id,
          blacklistedAt: new Date().toISOString(),
        };

        const saved = await dataManager.saveBlacklistedServers(blacklist);

        if (saved) {
          logger.info(
            `[BLACKLIST] ${message.author.tag} (${message.author.id}) blacklisted server ${serverId} for reason: ${reason}`
          );

          const guild = client.guilds.cache.get(serverId);

          if (guild) {
            try {
              await guild.leave();

              await message.reply({
                content: `${EMOJI[client.user.id].CHECK} Server \`${serverId}\` has been blacklisted and the bot has left the server.\nReason: ${reason}`,
                allowedMentions: { repliedUser: false },
              });
            } catch (error) {
              logger.error('Failed to leave blacklisted server:', error);

              await message.reply({
                content: `${EMOJI[client.user.id].CHECK} Server \`${serverId}\` has been blacklisted, but the bot failed to leave the server.\nReason: ${reason}`,
                allowedMentions: { repliedUser: false },
              });
            }
          } else {
            await message.reply({
              content: `${EMOJI[client.user.id].CHECK} Server \`${serverId}\` has been blacklisted.\nReason: ${reason}`,
              allowedMentions: { repliedUser: false },
            });
          }
        } else {
          throw new Error('Failed to save blacklist');
        }

        break;
      }

      case 'remove': {
        if (!args[1]) {
          return message.reply({
            content: 'Please specify a server ID to remove from the blacklist',
            allowedMentions: { repliedUser: false },
          });
        }

        const serverId = args[1];

        if (!blacklist.blacklistedServers[serverId]) {
          return message.reply({
            content: `Server with ID \`${serverId}\` is not blacklisted.`,
            allowedMentions: { repliedUser: false },
          });
        }

        const reason = blacklist.blacklistedServers[serverId].reason;

        delete blacklist.blacklistedServers[serverId];

        const saved = await dataManager.saveBlacklistedServers(blacklist);

        if (saved) {
          logger.info(
            `[BLACKLIST] ${message.author.tag} (${message.author.id}) removed server ${serverId} from blacklist (original reason: ${reason})`
          );

          await message.reply({
            content: `${EMOJI[client.user.id].CHECK} Server \`${serverId}\` has been removed from the blacklist.\nOriginal reason: ${reason}`,
            allowedMentions: { repliedUser: false },
          });
        } else {
          throw new Error('Failed to save blacklist');
        }

        break;
      }

      case 'list': {
        const blacklistedServers = Object.entries(blacklist.blacklistedServers);

        if (blacklistedServers.length === 0) {
          return message.reply({
            content: 'There are no blacklisted servers.',
            allowedMentions: { repliedUser: false },
          });
        }

        let formattedList = blacklistedServers.map(([id, data]: [string, BlacklistEntry]) => {
          const guild = client.guilds.cache.get(id);
          const serverName = guild ? `${guild.name} (${id})` : id;
          return `• **${serverName}**\n  • Reason: ${data.reason}\n  • By: <@${data.blacklistedBy}>\n  • When: <t:${Math.floor(new Date(data.blacklistedAt).getTime() / 1000)}:R>`;
        });
        let tooMany = false;
        if (formattedList.length > 10) {
          formattedList = formattedList.slice(0, 10);
          tooMany = true;
        }
        const embed = new EmbedBuilder()
          .setColor(0xff0000)
          .setTitle('Blacklisted Servers')
          .setDescription(
            formattedList.join('\n\n') +
              (tooMany
                ? `\n\n...and ${blacklistedServers.length - 10} more. Use \\list to view all.`
                : '')
          )
          .setFooter({
            text: `Requested by ${message.author.tag}`,
            iconURL: message.author.displayAvatarURL(),
          })
          .setTimestamp();
        await message.reply({ embeds: [embed], allowedMentions: { repliedUser: false } });
        break;
      }

      default:
        return message.reply({
          content: 'Invalid subcommand. Available subcommands: `add`, `remove`, `list`',
          allowedMentions: { repliedUser: false },
        });
    }
  } catch (error) {
    logger.error('Error in blacklist command:', error);
    message
      .reply({
        content: `An error occurred: ${error.message}`,
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
