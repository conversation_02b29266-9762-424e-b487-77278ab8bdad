# MyVC Discord Bot

A Discord bot that creates temporary voice channels when users join a designated "join to create" channel, offering extensive customization and management features. Optimized for 100+ concurrent users.

## Features

- **Dynamic Temporary Voice Channels**: Automatically creates voice channels when users join a designated "creator" channel.
- **Persistent User Settings**: Remembers user preferences for channel names, limits, privacy, and other settings, stored in MongoDB.
- **Automatic Channel Cleanup**: Efficiently deletes empty temporary channels.
- **Waiting Rooms**: Allows users to create and manage waiting rooms for their temporary channels.
- **Advanced Channel Control**:
    - **Privacy Settings**: Make channels public, private (invite-only), or locked.
    - **User Management**: Trust, block, kick, or invite specific users to channels.
    - **Channel Ownership**: Claim or transfer ownership of temporary channels.
    - **Customization**: Change channel name, user limit, and bitrate.
- **Slash and Text Commands**: Offers both modern slash commands and traditional prefix-based text commands.
- **Performance Optimized**: Designed for minimal API calls, efficient data handling (including a save queue for user settings), and quick response times.
- **Sharding Support**: Can be run in sharded mode for very large bots.
- **Guild Event Logging**: Tracks when the bot is added to or removed from servers, with optional webhook integration.
- **Error Logging**: Comprehensive error logging with optional webhook integration.
- **Customizable Prefix**: Server administrators can set a custom prefix for text commands.
- **Developer Friendly**: Includes commands for reloading modules and viewing bot info.


## Bot Permissions

Required Discord permissions for the bot to function correctly:
- Manage Channels
- Manage Roles (for channel permissions and private channels)
- View Channels
- Connect (to voice channels)
- Move Members (for managing users in temporary channels)
- Send Messages
- Embed Links (for formatted responses)
- Read Message History (for text commands)
- Use External Emojis (if used in responses)

An example invite link (replace `YOUR_CLIENT_ID` and adjust permissions bits if necessary):
```
https://discord.com/oauth2/authorize?client_id=YOUR_CLIENT_ID&permissions=16796753&scope=bot%20applications.commands
```
(The permission integer `16796753` corresponds to: View Channels, Manage Channels, Manage Roles, Send Messages, Embed Links, Connect, Move Members. Verify this against the bot's actual needs.)

## Commands

### Slash Commands (`/`)
- `/setup`: Sets up the MyVC system in your server (e.g., creates the "join to create" channel).
- `/help`: Displays help information about slash commands and bot features.
- `/ping`: Checks the bot's responsiveness and latency.

### Text Commands (Default prefix: `.`, configurable)
- `prefix [newPrefix]`: View or change the bot's command prefix for this server.
- `help [commandName]`: Displays help information for text commands.
- `ping`: Checks the bot's responsiveness.
- `activity`: (Likely developer command) Manages bot's presence/activity.
- `blacklist`: (Likely developer/admin command) Manages blacklisted users/guilds.
- `commands`: (Likely developer command) Lists or manages available commands.
- `info`: Displays information about the bot.
- `list`: (Likely related to listing channels, users, or settings).
- `reload [commandName]`: (Developer command) Reloads a specified command module.
- `shutdown`: (Developer command) Shuts down the bot.

_Note: Some text commands might be restricted to bot owners or administrators._

## Guild Event Logging

The bot logs detailed information about when it joins or leaves a guild:
- Logs can be stored locally (e.g., in `data/logs`).
- Events can be sent to a Discord channel via webhook for real-time monitoring if `GUILD_WEBHOOK_URL` is configured.
- A script `src/scripts/view-guild-logs.ts` (and its JavaScript equivalent) is available for viewing these logs from the console with filtering options.

```bash
# View all guild events
ts-node src/scripts/view-guild-logs.ts

# Filter by guild ID
ts-node src/scripts/view-guild-logs.ts --guild 123456789012345678

# Filter by event type (JOIN or LEAVE)
ts-node src/scripts/view-guild-logs.ts --event JOIN
```

## Discord Webhook Integration

The bot supports sending logs to Discord channels via webhooks for:
1.  **Error Logging**: Critical errors can be sent to a designated channel via `ERROR_WEBHOOK_URL`.
2.  **Guild Event Logging**: Server join/leave events can be sent via `GUILD_WEBHOOK_URL`.

Setup:
1.  Create webhooks in your Discord server (Server Settings → Integrations → Webhooks → New Webhook).
2.  Copy the webhook URL.
3.  Add the URLs to your `.env` file.
4.  Restart the bot.

Ensure webhook URLs are correct and the bot/webhook has permissions in the target channel.

## Troubleshooting Webhook Issues

- **Verify URL**: Double-check the webhook URL. Test with `curl`.
- **Check Permissions**: Ensure the webhook can send messages in the channel.
- **Inspect Bot Logs**: Look for webhook-related errors or warnings.
- **Recreate Webhook**: If issues persist, try creating a new webhook.
- **Common HTTP Errors**:
    - `405 Method Not Allowed`: Incorrect URL or deleted webhook.
    - `401 Unauthorized`: Invalid webhook token.
    - `429 Too Many Requests`: Rate limiting by Discord.

