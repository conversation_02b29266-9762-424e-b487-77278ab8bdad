import {
  ActionRow<PERSON>uilder,
  <PERSON>tonBuilder,
  ButtonStyle,
  VoiceChannel,
  EmbedBuilder,
} from 'discord.js';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'delete';

export const execute = async (interaction, client, userChannel) => {
  const deleteSource = `delete.execute:button:${interaction.customId}`;
  try {
    const confirmButton = new ButtonBuilder()
      .setCustomId(`delete_confirm_${userChannel.id}`)
      .setLabel('Confirm')
      .setStyle(ButtonStyle.Danger)
      .setEmoji(EMOJI[client.user.id].CHECK);

    const cancelButton = new ButtonBuilder()
      .setCustomId(`cancel_${userChannel.id}`)
      .setLabel('Cancel')
      .setStyle(ButtonStyle.Secondary)
      .setEmoji(EMOJI[client.user.id].CROSS);

    const row: any = new ActionRowBuilder().addComponents(confirmButton, cancelButton);

    await replyOrFollowUpEphemeral(interaction, {
      content: `🚨 **Are you sure you want to delete the channel "${userChannel.name}"?** This action cannot be undone.`,
      components: [row],
    });
  } catch (error) {
    logger.error(`${deleteSource}: Error sending delete confirmation: ${error.message}`);
    await handleInteractionError(deleteSource, error, interaction, client);
  }
};

/**
 * Handle the confirmation of channel deletion
 * Called when the delete_confirm_{channelId} button is clicked
 *
 * @param {Interaction} interaction - The interaction from the button click
 * @param {Client} client - The Discord client
 * @param {string} channelId - The ID of the channel to delete, extracted from the button ID
 */
export const handleConfirmation = async (interaction, client, channelId) => {
  const confirmSource = `delete.handleConfirmation:button:${interaction.customId}`;

  try {
    const targetChannel = await interaction.guild?.channels.fetch(channelId).catch(() => null);
    if (!targetChannel || !(targetChannel instanceof VoiceChannel)) {
      logger.warn(
        `${confirmSource}: Target channel ${channelId} not found or not a voice channel.`
      );
      const errorEmbed = new EmbedBuilder()
        .setColor('#FF0000')
        .setTitle(`${EMOJI[client.user.id].CROSS} Channel Not Found`)
        .setDescription('The target channel no longer exists or is invalid.')
        .setTimestamp();

      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
      return;
    }

    const isOwner = client.tempChannels.get(targetChannel.id) === interaction.user.id;
    if (!isOwner) {
      logger.warn(
        `${confirmSource}: User ${interaction.user.tag} attempted delete confirmation without ownership.`
      );
      const errorEmbed = new EmbedBuilder()
        .setColor('#FF0000')
        .setTitle(`${EMOJI[client.user.id].CROSS} Permission Denied`)
        .setDescription('You must be the owner of this channel to delete it.')
        .setTimestamp();

      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
      return;
    }

    try {
      logger.info(
        `User ${interaction.user.tag} confirmed deletion for channel ${targetChannel.name} (${channelId})`
      );

      await targetChannel.delete(`Deleted by owner ${interaction.user.tag}`);
      client.tempChannels.delete(channelId);
      logger.info(`Channel ${channelId} deleted successfully.`);

      const successEmbed = new EmbedBuilder()
        .setColor('#00FF00')
        .setTitle(`${EMOJI[client.user.id].CHECK} Channel Deleted`)
        .setDescription(`The channel "${targetChannel.name}" has been successfully deleted.`)
        .setTimestamp();

      await interaction.update({
        embeds: [successEmbed],
        components: [],
      });
    } catch (deleteError) {
      logger.error(
        `${confirmSource}: Failed to delete channel ${channelId}: ${deleteError.message}`
      );

      const errorEmbed = new EmbedBuilder()
        .setColor('#FF0000')
        .setTitle(`${EMOJI[client.user.id].CROSS} Deletion Failed`)
        .setDescription(
          'Failed to delete the channel. The bot might lack permissions or the channel was already deleted.'
        )
        .setTimestamp();

      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
    }
  } catch (error) {
    logger.error(
      `${confirmSource}: Unexpected error handling delete confirmation: ${error.message}`
    );

    const errorEmbed = new EmbedBuilder()
      .setColor('#FF0000')
      .setTitle(`${EMOJI[client.user.id].CROSS} Unexpected Error`)
      .setDescription('An unexpected error occurred. Please try again later.')
      .setTimestamp();

    if (!interaction.replied && !interaction.deferred) {
      await interaction.update({
        embeds: [errorEmbed],
        components: [],
      });
    }
  }
};

/**
 * Handle cancel button clicks
 * Called when cancel_{channelId} button is clicked
 *
 * @param {Interaction} interaction - The interaction from the button click
 */
export const handleCancel = async (interaction, client) => {
  const cancelSource = `delete.handleCancel:button:${interaction.customId}`;

  try {
    const cancelEmbed = new EmbedBuilder()
      .setColor('#00FF00')
      .setTitle(`${EMOJI[client.user.id].CHECK} Deletion Cancelled`)
      .setDescription('The channel deletion has been cancelled.')
      .setTimestamp();

    await interaction.update({
      embeds: [cancelEmbed],
      components: [],
    });

    logger.debug(`User ${interaction.user.tag} cancelled channel deletion`);
  } catch (error) {
    logger.error(`${cancelSource}: Failed to update cancel message: ${error.message}`);
  }
};
