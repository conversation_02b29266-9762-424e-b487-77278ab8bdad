/**
 * MyVC Discord Bot - Sharding Manager
 * Handles the creation and management of multiple bot shards
 * for improved performance and stability with many guilds
 */
import 'dotenv/config';
import { ShardingManager } from 'discord.js';
import path from 'path';
import logger from './utils/logger';
import dataManager from './utils/dataManager';

const SHARD_COUNT =
  process.env.SHARD_COUNT === 'auto'
    ? 'auto'
    : process.env.SHARD_COUNT
      ? parseInt(process.env.SHARD_COUNT, 10)
      : 'auto';

const SHARD_FILE = path.join(__dirname, 'index.js');

if (__filename.includes('dist/src/shard.js')) {
  logger.debug('Running from compiled JavaScript, adjusting shard file path');
}

/**
 * Initialize the database connection before starting shards
 */
async function initDatabase() {
  try {
    logger.info('Connecting to database before spawning shards...');
    await dataManager.connectToMongoDB();
    logger.info('Database connection successful. Ready to spawn shards.');
    return true;
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    return false;
  }
}

/**
 * Create and configure the sharding manager
 */
async function initShardManager() {
  const dbInitialized = await initDatabase();
  if (!dbInitialized) {
    logger.error('Cannot initialize shards without database connection. Exiting.');
    process.exit(1);
  }

  const manager = new ShardingManager(SHARD_FILE, {
    token: process.env.TOKEN,
    totalShards: SHARD_COUNT,
    respawn: true,
    shardArgs: process.argv.slice(2),
    execArgv: process.execArgv,
  });

  manager.on('shardCreate', shard => {
    logger.info(`Launched shard ${shard.id}`);

    shard.on('ready', () => {
      logger.info(`Shard ${shard.id} ready`);
    });

    shard.on('disconnect', () => {
      logger.warn(`Shard ${shard.id} disconnected`);
    });

    shard.on('reconnecting', () => {
      logger.info(`Shard ${shard.id} reconnecting`);
    });

    shard.on('death', childProcess => {
      const exitCodeInfo = childProcess.hasOwnProperty('exitCode')
        ? `with exit code ${(childProcess as any).exitCode}`
        : 'unexpectedly';
      logger.error(`Shard ${shard.id} died ${exitCodeInfo}`);
    });

    shard.on('error', error => {
      logger.error(`Shard ${shard.id} encountered an error:`, error);
    });
  });

  setInterval(
    async () => {
      try {
        const guildCounts = await manager.fetchClientValues('guilds.cache.size');
        const totalGuilds = guildCounts.reduce((acc: number, count: number) => acc + count, 0);

        const voiceConnectionCounts = await manager.broadcastEval(client => {
          return (client as any).tempChannels?.size || 0;
        });
        const totalVoiceConnections = voiceConnectionCounts.reduce(
          (acc: number, count: number) => acc + count,
          0
        );

        logger.info(`Total guilds: ${totalGuilds} across ${guildCounts.length} shards`);
        logger.info(`Active voice channels: ${totalVoiceConnections}`);

        await dataManager.saveData('shard_stats', {
          timestamp: new Date(),
          shardCount: guildCounts.length,
          totalGuilds,
          totalVoiceConnections,
          shardGuildCounts: guildCounts,
        });
      } catch (error) {
        logger.error('Failed to fetch shard statistics:', error);
      }
    },
    5 * 60 * 1000
  );

  return manager;
}

initShardManager()
  .then(manager => {
    logger.info(`Starting MyVC bot with ${SHARD_COUNT} shards`);

    manager
      .spawn({
        timeout: 60000,
        delay: 7500,
      })
      .catch(error => {
        logger.error('Failed to spawn shards:', error);
        process.exit(1);
      });
  })
  .catch(error => {
    logger.error('Failed to initialize sharding manager:', error);
    process.exit(1);
  });

process.on('SIGINT', handleShutdown);
process.on('SIGTERM', handleShutdown);

/**
 * Clean shutdown handler
 */
async function handleShutdown() {
  logger.info('Shutting down sharding manager...');

  try {
    await dataManager.cleanup();
    logger.info('Database connections closed.');

    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
}

process.on('uncaughtException', error => {
  logger.error('Uncaught Exception in Sharding Manager:', error);
});

process.on('unhandledRejection', reason => {
  const errorMessage = 'Unhandled Rejection in Sharding Manager';

  if (reason instanceof Error) {
    logger.error(errorMessage, reason);
  } else if (typeof reason === 'string') {
    logger.error(`${errorMessage}: ${reason}`);
  } else {
    logger.error(`${errorMessage}: ${String(reason)}`);
  }
});
