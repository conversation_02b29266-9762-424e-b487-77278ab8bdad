/**
 * Webhook Utility
 *
 * Handles sending messages to Discord webhooks for logging purposes.
 * Supports error reporting and guild event logging.
 */
import fetch from 'node-fetch';
import logger from './logger';

// Webhook URLs will be loaded from environment variables when needed
// This allows the environment to be updated without restarting the bot
function getErrorWebhookUrl(): string {
  return process.env.ERROR_WEBHOOK_URL || '';
}

function getGuildWebhookUrl(): string {
  return process.env.GUILD_WEBHOOK_URL || '';
}

// Log webhook configuration status on startup
(function checkWebhookConfig() {
  const errorUrl = getErrorWebhookUrl();
  const guildUrl = getGuildWebhookUrl();

  if (!errorUrl) {
    logger.info('Error webhook URL not configured. Error logging to Discord is disabled.');
  } else if (!isValidWebhookUrl(errorUrl)) {
    logger.warn('Invalid error webhook URL provided. Error logging to Discord is disabled.');
  } else {
    logger.info('Error webhook configured. Errors will be sent to Discord.');
  }

  if (!guildUrl) {
    logger.info('Guild webhook URL not configured. Guild event logging to Discord is disabled.');
  } else if (!isValidWebhookUrl(guildUrl)) {
    logger.warn('Invalid guild webhook URL provided. Guild event logging to Discord is disabled.');
  } else {
    logger.info('Guild webhook configured. Guild events will be sent to Discord.');
  }
})();

/**
 * Validates if a string is a valid Discord webhook URL
 * @param url The URL to validate
 * @returns boolean indicating if the URL is valid
 */
function isValidWebhookUrl(url: string): boolean {
  // More comprehensive validation
  try {
    const webhookUrl = new URL(url);
    return (
      (webhookUrl.hostname === 'discord.com' || webhookUrl.hostname === 'discordapp.com') &&
      webhookUrl.pathname.startsWith('/api/webhooks/') &&
      webhookUrl.pathname.split('/').length >= 4
    );
  } catch (error) {
    return false;
  }
}

/**
 * Sends a message to a Discord webhook
 * @param webhookUrl The Discord webhook URL
 * @param content The message content
 * @param embeds Optional embeds to include in the message
 * @returns Promise that resolves when the message is sent
 */
async function sendWebhookMessage(
  webhookUrl: string,
  content: string,
  embeds?: any[]
): Promise<boolean> {
  if (!webhookUrl || !isValidWebhookUrl(webhookUrl)) {
    // Silently fail with invalid URLs
    logger.debug('Invalid webhook URL or no URL provided');
    return false;
  }

  try {
    // Extract the ID and token from the webhook URL
    // Format: https://discord.com/api/webhooks/{id}/{token}
    const urlParts = webhookUrl.split('/');
    if (urlParts.length < 7) {
      logger.warn(
        `Invalid webhook URL format: ${webhookUrl.replace(/\/[^\/]+\/[^\/]+$/, '/****/****')}`
      );
      return false;
    }

    const webhookId = urlParts[5];
    const webhookToken = urlParts[6].split('?')[0].split('#')[0]; // Remove query params and fragments

    // Construct the API URL directly
    const apiUrl = `https://discord.com/api/webhooks/${webhookId}/${webhookToken}`;

    // Prepare the payload - only include non-empty fields
    const payload: any = {
      username: 'MyVC Bot',
      avatar_url: 'https://i.imgur.com/AfFp7pu.png', // Default Discord logo
    };

    // Only add content if it's not empty
    if (content && content.trim() !== '') {
      payload.content = content;
    }

    // Only add embeds if they exist and aren't empty
    if (embeds && embeds.length > 0) {
      payload.embeds = embeds;
    }

    // If we have neither content nor embeds, add a placeholder content
    if (!payload.content && (!payload.embeds || payload.embeds.length === 0)) {
      payload.content = '.'; // Discord requires either content or embeds
    }

    // Log the request details (masked for security)
    logger.debug(`Sending webhook to: ${apiUrl.replace(/\/[^\/]+\/[^\/]+$/, '/****/****')}`);
    logger.debug(`Webhook payload: ${JSON.stringify(payload)}`);

    // Make the request with explicit timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text();
      logger.warn(`Failed to send webhook message: ${response.status} ${errorText}`);

      // Additional debugging for specific error codes
      if (response.status === 405) {
        logger.warn(
          '405 Method Not Allowed error. This typically means the webhook URL is incorrect or the webhook has been deleted.'
        );
      } else if (response.status === 401 || response.status === 403) {
        logger.warn('Authentication error. The webhook token may be invalid.');
      } else if (response.status === 429) {
        logger.warn('Rate limit exceeded. The bot is sending too many webhook requests.');
      }

      return false;
    }

    logger.debug('Webhook message sent successfully');
    return true;
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        logger.warn('Webhook request timed out after 5 seconds');
      } else {
        logger.warn(`Error sending webhook message: ${error.message}`);
      }
    } else {
      logger.warn('Unknown error sending webhook message');
    }
    return false;
  }
}

/**
 * Sends an error message to the error webhook
 * @param errorMessage The error message
 * @param errorDetails Additional error details
 * @returns Promise that resolves when the message is sent
 */
export async function sendErrorWebhook(errorMessage: string, errorDetails?: any): Promise<boolean> {
  const webhookUrl = getErrorWebhookUrl();
  if (!webhookUrl) {
    return false; // Silently fail if webhook URL is not configured
  }

  const timestamp = new Date().toISOString();
  let content = `**[ERROR] ${timestamp}**\n${errorMessage}`;

  const embeds = [];

  if (errorDetails) {
    // Create an embed for the error details
    embeds.push({
      title: 'Error Details',
      color: 0xff0000, // Red
      description:
        typeof errorDetails === 'object'
          ? '```json\n' + JSON.stringify(errorDetails, null, 2) + '\n```'
          : String(errorDetails),
      timestamp: new Date().toISOString(),
    });
  }

  return sendWebhookMessage(webhookUrl, content, embeds);
}

/**
 * Sends a guild event message to the guild webhook
 * @param eventType The type of event (JOIN or LEAVE)
 * @param guildInfo Information about the guild
 * @returns Promise that resolves when the message is sent
 */
export async function sendGuildWebhook(
  eventType: 'JOIN' | 'LEAVE',
  guildInfo: any
): Promise<boolean> {
  const webhookUrl = getGuildWebhookUrl();
  if (!webhookUrl) {
    return false; // Silently fail if webhook URL is not configured
  }

  const isJoin = eventType === 'JOIN';
  const color = isJoin ? 0x00ff00 : 0xff0000; // Green for join, red for leave
  const title = isJoin ? '🟢 Bot Added to Server' : '🔴 Bot Removed from Server';

  // Create fields based on event type
  const fields = [];

  // Common fields for both event types
  fields.push(
    { name: 'Server Name', value: guildInfo.guildName, inline: true },
    { name: 'Server ID', value: guildInfo.guildId, inline: true },
    { name: 'Owner ID', value: guildInfo.ownerID || 'Unknown', inline: true }
  );

  if (isJoin) {
    // Fields specific to join events
    fields.push(
      { name: 'Member Count', value: String(guildInfo.memberCount || 'Unknown'), inline: true },
      { name: 'Region', value: guildInfo.region || 'Unknown', inline: true },
      { name: 'Created At', value: new Date(guildInfo.createdAt).toLocaleString(), inline: true }
    );

    if (guildInfo.channels) {
      fields.push({
        name: 'Channels',
        value: `Total: ${guildInfo.channels.total || 0}\nText: ${guildInfo.channels.text || 0}\nVoice: ${guildInfo.channels.voice || 0}\nCategories: ${guildInfo.channels.categories || 0}`,
        inline: true,
      });
    }
  } else {
    // Fields specific to leave events
    fields.push({ name: 'Reason', value: guildInfo.reason || 'Unknown', inline: true });

    if (guildInfo.joinedAt) {
      const joinedAt = new Date(guildInfo.joinedAt).toLocaleString();
      const leftAt = new Date(guildInfo.leftAt).toLocaleString();
      const duration = Math.floor(
        (new Date(guildInfo.leftAt).getTime() - new Date(guildInfo.joinedAt).getTime()) /
          (1000 * 60 * 60 * 24)
      );

      fields.push({
        name: 'Duration',
        value: `${duration} days\nJoined: ${joinedAt}\nLeft: ${leftAt}`,
        inline: true,
      });
    }
  }

  const embeds = [
    {
      title,
      color,
      fields,
      timestamp: new Date().toISOString(),
    },
  ];

  return sendWebhookMessage(webhookUrl, '', embeds);
}

export default {
  sendErrorWebhook,
  sendGuildWebhook,
};
