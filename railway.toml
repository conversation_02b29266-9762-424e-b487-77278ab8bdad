[build]
builder = "NIXPACKS"

[deploy]
startCommand = "node --max-old-space-size=512 --optimize-for-size --gc-interval=100 dist/src/index.js"
healthcheckPath = "/health"
healthcheckTimeout = 100
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[variables]
NIXPACKS_BUILD_CMD = "npm run dev"
NODE_ENV = "production"
NODE_OPTIONS = "--max-old-space-size=512 --optimize-for-size --gc-interval=100"
