/**
 * Info Command
 * Displays information about the bot in Carl-bot style
 */
import { EmbedBuilder } from 'discord.js';
import { Message } from 'discord.js';
import { formatUptime, formatBytes } from '../utils/helpers';
import { DEVELOPER_IDS } from '../constants/devs';
import logger from '../utils/logger';
import { ExtendedClient } from '../types';
import { SERVER_INVITE_LINK } from '../constants';

export const name = 'info';
export const description = 'Displays information about the bot';
export const devOnly = false;
export const adminOnly = false;

/**
 * Fetch developer info for the embed
 * @param {ExtendedClient} client - Discord.js client
 * @returns {string} - Formatted developer info
 */
async function fetchDeveloperInfo(client: ExtendedClient): Promise<string> {
  try {
    return DEVELOPER_IDS.map(id => `<@${id}>`).join(', ');
  } catch (error) {
    logger.error('Error fetching developer info:', error);
    return DEVELOPER_IDS.map(id => `<@${id}>`).join(', ');
  }
}

export const execute = async (message: Message, _args: string[], client: ExtendedClient) => {
  try {
    const memoryUsage = process.memoryUsage();
    const totalMemory = memoryUsage.rss;

    const cpuUsage = process.cpuUsage();
    const cpuPercent =
      ((cpuUsage.user + cpuUsage.system) / 1000000 / (process.uptime() * 1000)) * 100;

    const totalMembers = client.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0);

    const totalChannels = client.guilds.cache.reduce(
      (acc, guild) => acc + guild.channels.cache.size,
      0
    );

    const totalVoiceChannels = client.guilds.cache.reduce(
      (acc, guild) => acc + guild.channels.cache.filter(c => c.type === 2).size,
      0
    );

    const stats = global.getStats
      ? global.getStats()
      : {
          totalMessagesProcessed: 0,
          totalCommandsUsed: 0,
          startTime: Date.now(),
        };

    const uptime = formatUptime(client.uptime);

    const uptimeSeconds = (Date.now() - stats.startTime) / 1000;
    const messagesPerSecond =
      uptimeSeconds > 0 ? (stats.totalMessagesProcessed / uptimeSeconds).toFixed(1) : '0.0';

    const developerInfo = await fetchDeveloperInfo(client);

    const infoEmbed = new EmbedBuilder()
      .setColor(0x36393f)
      .setAuthor({
        name: `${client.user.username}`,
        iconURL: client.user.displayAvatarURL(),
      })
      .setDescription(
        [
          '✨ **MyVC** — Effortless, intelligent voice channel management.',
          '─────────────────────────────',
          '🔗 **Links**',
          `• [Support](${SERVER_INVITE_LINK})`,
          '─────────────────────────────',
          '📝 **About**',
          '> MyVC automatically creates, manages, and cleans up voice channels for you—so your community always has the perfect space to connect. Enjoy a smoother, smarter, and spam-free Discord experience.',
          '',
          '🌍 **Global Sync**',
          '> Your user settings are saved globally—enjoy a seamless experience anywhere you use MyVC!',
          '',
          '_Join thousands of communities making the switch to MyVC!_',
        ].join('\n')
      )
      .addFields(
        {
          name: '👥 Members',
          value: `**${totalMembers.toLocaleString()}** total\n${client.guilds.cache.size.toLocaleString()} servers`,
          inline: true,
        },
        {
          name: '💾 Process',
          value: `${formatBytes(totalMemory)} RAM\n${cpuPercent.toFixed(1)}% CPU`,
          inline: true,
        },
        {
          name: '⏱️ Uptime',
          value: uptime,
          inline: true,
        },
        {
          name: '👨‍💻 Developers',
          value: developerInfo,
          inline: false,
        }
      )
      .setFooter({
        text: `Made with ❤️ with Discord.js`,
        iconURL:
          'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/javascript/javascript-original.svg',
      });

    await message.reply({ embeds: [infoEmbed], allowedMentions: { repliedUser: false } });
  } catch (error) {
    logger.error('Error in info command:', error);
    message
      .reply({
        content: 'An error occurred while getting bot info.',
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
