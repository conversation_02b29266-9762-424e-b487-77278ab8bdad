import {
  Client,
  Discord<PERSON>IError,
  Guild,
  GuildChannel,
  GuildMember,
  Interaction,
  InteractionReplyOptions,
  MessageFlags,
} from 'discord.js';
import logger from './logger';
import { EMOJI } from '../constants/emoji';

const DISCORD_ERROR_CODES = {
  UNKNOWN_INTERACTION: 10062,
  INTERACTION_TIMEOUT: 10063,
  UNKNOWN_CHANNEL: 10003,
  UNKNOWN_GUILD: 10004,
  UNKNOWN_MEMBER: 10007,
  MISSING_PERMISSIONS: 50013,
  CANNOT_EXECUTE_ON_DM: 50003,
  MISSING_ACCESS: 50001,
  RATE_LIMITED: 429,
  INVALID_FORM_BODY: 50035,
  INVALID_WEBHOOK_TOKEN: 50027,
};

/**
 * Centralized error handler for interaction events
 * @param source - The function or context where the error occurred
 * @param error - The error object
 * @param interaction - The interaction that triggered the error
 * @returns Promise<void>
 */
export async function handleInteractionError(
  source: string,
  error: any,
  interaction: Interaction,
  client: Client
): Promise<void> {
  if (error instanceof DiscordAPIError) {
    logger.error(`[API ERROR] ${source}: Code ${error.code} - ${error.message}`);

    switch (error.code) {
      case DISCORD_ERROR_CODES.UNKNOWN_INTERACTION:
      case DISCORD_ERROR_CODES.INTERACTION_TIMEOUT:
        logger.warn(
          `API Error: Interaction no longer valid (Code: ${error.code}). Source: ${source}`
        );

        return;

      case DISCORD_ERROR_CODES.MISSING_PERMISSIONS:
      case DISCORD_ERROR_CODES.MISSING_ACCESS:
        if (interaction?.guild && interaction.channel) {
          logPermissionDiagnostic(
            source,
            error,
            interaction as Interaction & {
              guild: Guild;
              channel: GuildChannel;
              member: GuildMember;
            }
          );
        } else {
          logger.error(
            `API Error: Missing Permissions/Access but interaction context is incomplete. Guild: ${!!interaction?.guild}, Channel: ${!!interaction?.channel}`
          );
        }
        break;

      case DISCORD_ERROR_CODES.UNKNOWN_CHANNEL:
        logger.error('API Error: Referenced channel not found.');
        break;
      case DISCORD_ERROR_CODES.UNKNOWN_GUILD:
        logger.error('API Error: Referenced guild not found.');
        break;
      case DISCORD_ERROR_CODES.UNKNOWN_MEMBER:
        logger.error('API Error: Referenced member not found.');
        break;
      case DISCORD_ERROR_CODES.RATE_LIMITED:
        logger.warn(`API Error: Rate limited. Source: ${source}`);
        break;

      default:
        logger.error(`Unhandled Discord API Error (Code: ${error.code}): ${error.message}`);
    }
  } else if (error instanceof Error) {
    logger.error(`[JS ERROR] ${source}: ${error.message}`);
    if (error.stack) {
      logger.error(`Stack Trace (partial): ${error.stack.split('\n').slice(0, 3).join('\n')}`);
    }
  } else {
    logger.error(`[UNKNOWN ERROR] ${source}: ${JSON.stringify(error)}`);
  }

  if (
    interaction &&
    interaction.isRepliable() &&
    error.code !== DISCORD_ERROR_CODES.UNKNOWN_INTERACTION &&
    error.code !== DISCORD_ERROR_CODES.INTERACTION_TIMEOUT
  ) {
    try {
      const content = `${EMOJI[client.user.id].UNTRUST} An error occurred while processing your request. Please try again later.`;
      const options: InteractionReplyOptions = { content, flags: MessageFlags.Ephemeral };

      if (interaction.deferred || interaction.replied) {
        await interaction.followUp(options).catch(() => {});
      } else {
        await interaction.reply(options).catch(() => {});
      }
    } catch (respondError: any) {
      logger.error(`Failed to send error response to user: ${respondError.message}`);
    }
  }
}

/**
 * Log detailed permission diagnostics for interaction errors
 * @param source - Source of the error
 * @param error - The error object
 * @param interaction - The interaction that triggered the error (must be in a guild context)
 */
export function logPermissionDiagnostic(
  source: string,
  error: DiscordAPIError,
  interaction: Interaction & { guild: Guild; channel: GuildChannel; member: GuildMember }
): void {
  try {
    const { guild, channel, member, user } = interaction;
    const bot = guild.members.me;

    if (!bot) {
      logger.error(`Cannot diagnose permissions: Bot member object not found in guild ${guild.id}`);
      return;
    }

    const botRoles = bot.roles.cache.map(r => ({ name: r.name, id: r.id }));
    const guildRoles = guild.roles.cache
      .map(r => ({ name: r.name, id: r.id }))
      .sort((a, b) => a.name.localeCompare(b.name));
    const botGuildPerms = bot.permissions.toArray();
    const botChannelPerms = channel ? bot.permissionsIn(channel).toArray() : null;

    logger.error(`
========== INTERACTION PERMISSION ERROR ==========
Source: ${source}
Error: ${error.message} (Code: ${error.code})
Guild: ${guild.name} (${guild.id})
Channel: ${channel.name} (${channel.id}, Type: ${channel.type})
User: ${user.tag} (${user.id})

Bot Info:
- ID: ${bot.id}
- Highest Role: ${bot.roles.highest.name}
- Guild Permissions: ${botGuildPerms.join(', ') || 'None'}
${botChannelPerms ? `- Channel Permissions: ${botChannelPerms.join(', ') || 'None'}` : '- Channel Permissions: N/A (Could not resolve)'}

Top 5 Guild Roles:
${guildRoles
  .slice(0, 5)
  .map(r => `- ${r.name} (${r.id})`)
  .join('\n')}

Bot Roles (${botRoles.length}):
${botRoles
  .slice(0, 10)
  .map(r => `- ${r.name} (${r.id})`)
  .join('\n')}${botRoles.length > 10 ? '\n...' : ''}

User Info:
- ID: ${user.id}
- Highest Role: ${member?.roles.highest.name ?? 'N/A'}
================================================
    `);
  } catch (diagError: any) {
    logger.error(`Error during permission diagnostics: ${diagError.message}`);
  }
}

/**
 * Defers an interaction ephemerally, handling potential "Unknown Interaction" errors.
 * @param interaction The interaction to defer.
 * @returns True if deferral was successful or interaction was already deferred, false otherwise.
 */
export async function deferReplyEphemeral(interaction: Interaction): Promise<boolean> {
  if (!interaction.isRepliable()) return false;

  if (interaction.deferred || interaction.replied) {
    return true;
  }

  try {
    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
    return true;
  } catch (error: any) {
    if (
      error instanceof DiscordAPIError &&
      (error.code === DISCORD_ERROR_CODES.UNKNOWN_INTERACTION ||
        error.code === DISCORD_ERROR_CODES.INTERACTION_TIMEOUT)
    ) {
      logger.warn(`Interaction ${interaction.id} already expired before deferral.`);
    } else {
      logger.error(`Failed to defer interaction ${interaction.id}: ${error.message}`);
    }
    return false;
  }
}

/**
 * Sends or edits an interaction reply ephemerally.
 * Handles whether the interaction was already deferred or replied to.
 * @param interaction The interaction to reply to.
 * @param options The reply options (content, components, embeds, etc.).
 * @returns Promise<void>
 */
export async function replyOrFollowUpEphemeral(
  interaction: Interaction,
  options: InteractionReplyOptions
): Promise<void> {
  if (!interaction.isRepliable()) return;

  options.ephemeral = true;

  try {
    if (interaction.deferred || interaction.replied) {
      await interaction.followUp(options);
    } else {
      await interaction.reply(options);
    }
  } catch (error: any) {
    if (
      error instanceof DiscordAPIError &&
      (error.code === DISCORD_ERROR_CODES.UNKNOWN_INTERACTION ||
        error.code === DISCORD_ERROR_CODES.INTERACTION_TIMEOUT)
    ) {
      logger.warn(`Interaction ${interaction.id} expired before reply/followUp.`);
    } else {
      logger.error(`Failed to reply/followUp interaction ${interaction.id}: ${error.message}`);
    }
  }
}
