import { ActionRowBuilder, StringSelectMenuBuilder } from 'discord.js';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'region';

const regions = [
  { label: 'Automatic', value: 'auto', description: 'Recommended default' },
  { label: 'Brazil', value: 'brazil' },
  { label: 'Europe', value: 'europe' },
  { label: 'Hong Kong', value: 'hongkong' },
  { label: 'India', value: 'india' },
  { label: 'Japan', value: 'japan' },
  { label: 'Rotterdam', value: 'rotterdam' },
  { label: 'Russia', value: 'russia' },
  { label: 'Singapore', value: 'singapore' },
  { label: 'South Africa', value: 'southafrica' },
  { label: 'Sydney', value: 'sydney' },
  { label: 'US Central', value: 'us-central' },
  { label: 'US East', value: 'us-east' },
  { label: 'US South', value: 'us-south' },
  { label: 'US West', value: 'us-west' },
];

export const execute = async (interaction, client, userChannel) => {
  try {
    const currentRegion = userChannel.rtcRegion || 'auto';

    const regionSelect = new StringSelectMenuBuilder()
      .setCustomId(`region_select_${userChannel.id}`)
      .setPlaceholder('Select voice region')
      .addOptions(
        regions.map(region => ({
          label: region.label,
          value: region.value,
          description: region.description,
          default: region.value === currentRegion,
        }))
      );

    const row: any = new ActionRowBuilder().addComponents(regionSelect);

    await replyOrFollowUpEphemeral(interaction, {
      content: 'Select the preferred voice server region for your channel:',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending region select menu: ${error.message}`);
    await handleInteractionError('region.execute', error, interaction, client);
  }
};

export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `region.handleSelectMenu:selectMenu:${interaction.customId}`;

  if (!interaction.isStringSelectMenu()) return;

  console.log('region value ', interaction.values);

  try {
    const selectedRegion = interaction.values[0];
    const userId = interaction.user.id;
    logger.debug(
      `${menuSource}: User ${userId} selected region "${selectedRegion}" for channel ${targetChannel.id}`
    );

    const regionValue = selectedRegion === 'auto' ? null : selectedRegion;
    const regionLabel = regions.find(r => r.value === selectedRegion)?.label || 'Unknown';

    await targetChannel.setRTCRegion(regionValue, `Region set by owner ${userId}`);
    logger.info(
      `Channel ${targetChannel.id} region set to "${selectedRegion}" (${regionLabel}) by ${userId}`
    );

    try {
      logger.debug(`Updated region preference for user ${userId}`);
    } catch (settingError) {
      logger.error(`Failed to save region preference for user ${userId}: ${settingError}`);
    }

    await replyOrFollowUpEphemeral(interaction, {
      content: `${EMOJI[client.user.id].CHECK} Voice region set to ${regionLabel}.`,
      components: [],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle region select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};
