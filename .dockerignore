# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
out/
*.tsbuildinfo

# Environment files
.env
.env.*
!.env.example

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Testing and coverage
coverage/
.nyc_output/

# IDE and editor files
.idea/
.vscode/
*.sublime-workspace
*.sublime-project
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Documentation
README.md
RAILWAY_DEPLOYMENT.md
*.md

# Development files
nodemon.json
.prettierrc
.eslintrc*

# Database files
data/
mongodb/

# Temporary files
*.tmp
*.temp
.cache/

# Railway specific
railway.toml

# Google Cloud specific
.gcloudignore
