import { ActivityType } from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';

export const name = 'ready';
export const once = true;
export const execute = async client => {
  logger.info(`Logged in as ${client.user.tag}!`);
  logger.info(`Bo<PERSON> is ready to serve in ${client.guilds.cache.size} guild(s)`);

  client.user.setPresence({
    activities: [
      {
        name: 'temporary voice channels',
        type: ActivityType.Watching,
      },
    ],
    status: 'online',
  });

  try {
    await dataManager.preloadGuildPrefixes();
    logger.info('Guild prefixes preloaded for real-time access');
  } catch (err) {
    logger.error('Error preloading guild prefixes:', err);
  }

  try {
    global.loadTempChannels();

    logger.info('Running startup cleanup for voice channels...');
    await global.startupCleanup();
  } catch (error) {
    logger.error('Error during startup cleanup:', error);
  }

  const blacklist: any = dataManager.loadBlacklistedServers();
  const blacklistedIds = Object.keys(blacklist.blacklistedServers || {});

  if (blacklistedIds.length > 0) {
    logger.info(`Found ${blacklistedIds.length} blacklisted server(s)`);

    let leftCount = 0;

    for (const serverId of blacklistedIds) {
      const guild = client.guilds.cache.get(serverId);

      if (guild) {
        const blacklistInfo = blacklist.blacklistedServers[serverId];
        logger.warn(
          `Leaving blacklisted server: ${guild.name} (${guild.id}), reason: ${blacklistInfo.reason}`
        );

        try {
          await guild.leave();
          leftCount++;
        } catch (error) {
          logger.error(`Failed to leave blacklisted server ${guild.id}:`, error);
        }
      }
    }

    if (leftCount > 0) {
      logger.info(`Left ${leftCount} blacklisted server(s)`);
    }
  }
};
