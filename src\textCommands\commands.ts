/**
 * Commands Command
 * Allows developers to load and unload specific commands
 */
import { EmbedBuilder } from 'discord.js';
import { Message, Client, Collection } from 'discord.js';
import fs from 'fs';
import path from 'path';
import logger from '../utils/logger';
import { EMOJI } from '../constants/emoji';

interface TextCommand {
  name: string;
  description?: string;
  usage?: string;
  devOnly?: boolean;
  adminOnly?: boolean;
  execute: (message: Message, args: string[], client: Client) => Promise<any>;
}

declare module 'discord.js' {
  interface Client {
    textCommands: Collection<string, TextCommand>;
  }
}

export const name = 'commands';
export const description = 'Manage bot commands (load/unload)';
export const usage = 'commands <list/unload/load> [command_name]';
export const devOnly = true;
export const adminOnly = false;

export const execute = async (message: Message, args: string[], client: Client) => {
  try {
    if (!args[0]) {
      return message.reply({
        content: 'Please specify a subcommand: `list`, `unload`, or `load`',
        allowedMentions: { repliedUser: false },
      });
    }

    const subCommand = args[0].toLowerCase();

    switch (subCommand) {
      case 'list': {
        const textCommandsPath = path.join(__dirname, '../textCommands');
        const textCommandFiles = fs
          .readdirSync(textCommandsPath)
          .filter(file => file.endsWith('.ts'));

        const loadedCommands = Array.from(client.textCommands.keys());

        const availableCommands = textCommandFiles.map(file => file.split('.')[0]);

        const unloadedCommands = availableCommands.filter(cmd => !loadedCommands.includes(cmd));

        const embed = new EmbedBuilder()
          .setColor(0x5865f2)
          .setTitle('Bot Commands Status')
          .setDescription(
            `Total: ${availableCommands.length} commands (${loadedCommands.length} loaded, ${unloadedCommands.length} unloaded)`
          )
          .setFooter({
            text: `Requested by ${message.author.tag}`,
            iconURL: message.author.displayAvatarURL(),
          })
          .setTimestamp();

        if (loadedCommands.length > 0) {
          loadedCommands.sort();

          embed.addFields({
            name: `${EMOJI[client.user.id].CHECK} Loaded Commands`,
            value: loadedCommands
              .map(cmd => {
                const command = client.textCommands.get(cmd) as TextCommand;
                const permissionInfo = command.devOnly
                  ? ' (Dev Only)'
                  : command.adminOnly
                    ? ' (Admin Only)'
                    : '';
                return `\`${cmd}\`${permissionInfo}`;
              })
              .join('\n'),
          });
        }

        if (unloadedCommands.length > 0) {
          unloadedCommands.sort();

          embed.addFields({
            name: `${EMOJI[client.user.id].CROSS} Unloaded Commands`,
            value: unloadedCommands.map(cmd => `\`${cmd}\``).join('\n'),
          });
        }

        await message.reply({ embeds: [embed], allowedMentions: { repliedUser: false } });
        break;
      }

      case 'unload': {
        if (!args[1]) {
          return message.reply({
            content: 'Please specify a command name to unload',
            allowedMentions: { repliedUser: false },
          });
        }

        const commandName = args[1].toLowerCase();

        if (!client.textCommands.has(commandName)) {
          return message.reply({
            content: `${EMOJI[client.user.id].CROSS} Command \`${commandName}\` is not loaded or doesn't exist.`,
            allowedMentions: { repliedUser: false },
          });
        }

        if (['commands', 'reload'].includes(commandName)) {
          return message.reply({
            content: `${EMOJI[client.user.id].CROSS} Cannot unload core command \`${commandName}\`. This would break the command system.`,
            allowedMentions: { repliedUser: false },
          });
        }

        const result = global.unloadCommand(commandName);

        if (result.success) {
          logger.info(`Command ${commandName} unloaded by ${message.author.tag}`);
          return message.reply({
            content: `${EMOJI[client.user.id].CHECK} Successfully unloaded command \`${commandName}\`.`,
            allowedMentions: { repliedUser: false },
          });
        } else {
          logger.error(`Failed to unload command ${commandName}:`, result.message);
          return message.reply({
            content: `${EMOJI[client.user.id].CROSS} Failed to unload command \`${commandName}\`: ${result.message}`,
            allowedMentions: { repliedUser: false },
          });
        }
      }

      case 'load': {
        if (!args[1]) {
          return message.reply({
            content: 'Please specify a command name to load',
            allowedMentions: { repliedUser: false },
          });
        }

        const commandName = args[1].toLowerCase();

        if (client.textCommands.has(commandName)) {
          return message.reply({
            content: `${EMOJI[client.user.id].CROSS} Command \`${commandName}\` is already loaded. Use \`reload\` instead.`,
            allowedMentions: { repliedUser: false },
          });
        }

        const result = global.loadCommand(commandName);

        if (result.success) {
          logger.info(`Command ${commandName} loaded by ${message.author.tag}`);
          return message.reply({
            content: `${EMOJI[client.user.id].CHECK} Successfully loaded command \`${commandName}\`.`,
            allowedMentions: { repliedUser: false },
          });
        } else {
          logger.error(`Failed to load command ${commandName}:`, result.message);
          return message.reply({
            content: `${EMOJI[client.user.id].CROSS} Failed to load command \`${commandName}\`: ${result.message}`,
            allowedMentions: { repliedUser: false },
          });
        }
      }

      default:
        return message.reply({
          content: 'Invalid subcommand. Available subcommands: `list`, `unload`, `load`',
          allowedMentions: { repliedUser: false },
        });
    }
  } catch (error) {
    logger.error('Error in commands command:', error);
    message
      .reply({
        content: 'An error occurred while managing commands.',
        allowedMentions: { repliedUser: false },
      })
      .catch(err => logger.error('Error sending error message:', err));
  }
};
