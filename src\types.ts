import {
  ClientEvents,
  Collection,
  CommandInteraction,
  Client as DiscordClient,
  Message,
  SlashCommandBuilder,
  SlashCommandOptionsOnlyBuilder,
} from 'discord.js';

export interface UserSettings {
  name?: string;
  channelName?: string;
  userLimit?: number;
  isPrivate?: boolean;
  isBitrate?: boolean;
  autoDelete?: boolean;
  trustedUsers?: string[];
  blockedUsers?: string[];
  isLocked?: boolean;
  hasWaitingRoom?: boolean;
  waitingRoomId?: string;
  ownerRoleId?: string;
  bitrate?: number;
  rtcRegion?: string;
  permissionOverwrites?: any[];
  createdAt?: number;
  lastModified?: number;
  lastUpdated?: Date;
}

export interface GuildSettings {
  joinChannelId?: string;
  categoryId?: string;
  prefix?: string;
}

export interface TempChannelInfo {
  userId: string;
  channelId: string;
  guildId: string;
  waitingRoomId?: string;
}

export interface BotStats {
  channelsCreated: number;
  messagesProcessed: number;
  commandsUsed: number;
  uptime: number;
  lastSave: number;
}

export interface CooldownInfo {
  userId: string;
  action: string;
  timestamp: number;
  channelId?: string;
}

export interface OperationResult {
  success: boolean;
  error?: string;
  data?: any;
}

export interface PermissionCheckResult {
  success: boolean;
  missing: string[];
}

export interface ExtendedClient extends DiscordClient {
  commands: Collection<string, any>;
  textCommands: Collection<string, any>;
  tempChannels: Map<string, TempChannelInfo>;
  userSettings: Map<string, UserSettings>;
  defaultSettings: Map<string, GuildSettings>;
  cooldowns: Collection<string, CooldownInfo[]>;
  pendingGuildSettings?: Map<
    string,
    {
      joinChannelId: string;
      categoryId: string;
      pendingSince: Date;
      error?: string;
    }
  >;
}

export const COOLDOWN_TIERS = {
  LIGHT: 3,
  MODERATE: 5,
  HEAVY: 10,
  CRITICAL: 15,
};

export const ERROR_CODES = {
  UNKNOWN_CHANNEL: 10003,
  UNKNOWN_GUILD: 10004,
  UNKNOWN_MEMBER: 10007,
  MISSING_PERMISSIONS: 50013,
  UNKNOWN_MESSAGE: 10008,
  MAX_CHANNELS: 30005,
  CANNOT_EXECUTE_ON_DM: 50003,
  RATE_LIMITED: 20028,
  INVALID_WEBHOOK_TOKEN: 50027,
  MISSING_ACCESS: 50001,
  UNKNOWN_INTERACTION: 10062,
  INTERACTION_TIMEOUT: 10063,
  INVALID_FORM_BODY: 50035,
};

/**
 * Interface for Command objects
 */
export interface Command {
  data:
    | SlashCommandBuilder
    | SlashCommandOptionsOnlyBuilder
    | Omit<SlashCommandBuilder, 'addSubcommand' | 'addSubcommandGroup'>;
  execute: (interaction: CommandInteraction, client: ExtendedClient) => Promise<void>;
}

/**
 * Interface for TextCommand objects
 */
export interface TextCommand {
  name: string;
  description: string;
  aliases?: string[];
  execute: (message: Message, args: string[], client: ExtendedClient) => Promise<void>;
}

/**
 * Interface for Event objects
 */
export interface Event {
  name: keyof ClientEvents;
  once?: boolean;
  execute: (...args: any[]) => Promise<void>;
}
