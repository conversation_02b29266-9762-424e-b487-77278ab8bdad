import {
  ActionRow<PERSON>uilder,
  ButtonBuilder,
  ButtonStyle,
  ChannelType,
  Collection,
  EmbedBuilder,
  GuildChannel,
  Message,
  ModalBuilder,
  TextInputBuilder,
  TextInputStyle,
  VoiceChannel,
} from 'discord.js';
import logger from '../utils/logger';
import { handleInteractionError, replyOrFollowUpEphemeral } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';
import { SERVER_INVITE_LINK } from '../constants';
import { ExtendedClient } from '../types';

const guildWelcomeCooldowns = new Set<string>();
const WELCOME_COOLDOWN_MS = 10;

/**
 * Sends a welcome message to a newly created temporary voice channel
 * Includes a purge button that only the channel owner can use
 * Applies a per-guild cooldown to prevent spam.
 * @param {VoiceChannel} voiceChannel - The newly created voice channel object (might be stale)
 * @param {string} ownerId - The ID of the channel owner
 * @param {ExtendedClient} client - The Discord client
 */
export async function sendWelcomeMessage(
  voiceChannel: VoiceChannel,
  ownerId: string,
  client: ExtendedClient
) {
  const guildId = voiceChannel.guild.id;
  const channelId = voiceChannel.id;

  const currentChannel = voiceChannel.guild.channels.cache.get(channelId);
  if (!currentChannel || !(currentChannel instanceof VoiceChannel)) {
    logger.warn(
      `sendWelcomeMessage: Channel ${channelId} no longer exists or is not a voice channel. Skipping message.`
    );
    return;
  }

  const freshVoiceChannel = currentChannel as VoiceChannel;

  if (guildWelcomeCooldowns.has(guildId)) {
    logger.warn(
      `Skipping welcome message for channel ${freshVoiceChannel.name} (${channelId}) in guild ${guildId} due to cooldown.`
    );
    return;
  }

  try {
    guildWelcomeCooldowns.add(guildId);

    setTimeout(() => {
      guildWelcomeCooldowns.delete(guildId);
      logger.debug(`Guild ${guildId} removed from welcome message cooldown.`);
    }, WELCOME_COOLDOWN_MS);

    const guild = freshVoiceChannel.guild;
    const interfaceChannel = guild.channels.cache.find(channel =>
      channel.name.toLowerCase().includes('interface')
    );

    const owner = await client.users.fetch(ownerId).catch(() => null);
    if (!owner) {
      logger.error(`Could not fetch user with ID ${ownerId} for welcome message`);

      return;
    }

    const welcomeEmbed = new EmbedBuilder()
      .setTitle('🎉 Voice Channel Created!')
      .setDescription(
        `Welcome <@${ownerId}>, you can customize your channel in ${
          interfaceChannel ? `<#${interfaceChannel.id}>` : 'the interface channel'
        }. Enjoy!`
      )
      .setColor('#3498db')
      .setTimestamp();

    const purgeButton = new ButtonBuilder()
      .setCustomId(`purge_button_${channelId}`)
      .setLabel('Purge Messages')
      .setStyle(ButtonStyle.Secondary)
      .setEmoji(EMOJI[client.user.id].PURGE);

    const supportButton = new ButtonBuilder()
      .setLabel('Support Server')
      .setStyle(ButtonStyle.Link)
      .setURL(SERVER_INVITE_LINK)
      .setEmoji(EMOJI[client.user.id].SUPPORT);

    const row = new ActionRowBuilder<ButtonBuilder>().addComponents(purgeButton, supportButton);

    try {
      await freshVoiceChannel.send({
        embeds: [welcomeEmbed],
        components: [row],
      });

      logger.info(
        `Sent welcome message to channel ${freshVoiceChannel.name} for user ${owner.tag}`
      );
    } catch (error) {
      logger.error(`Error sending welcome message in voice channel ${channelId}: ${error.message}`);

      const textChannel = guild.channels.cache.find(
        channel => channel.type === ChannelType.GuildText && channel.name === freshVoiceChannel.name
      );

      if (textChannel && textChannel.type === ChannelType.GuildText) {
        try {
          await textChannel.send({
            embeds: [welcomeEmbed],
            components: [row],
          });
          logger.info(
            `Sent welcome message to associated text channel ${textChannel.name} as fallback`
          );
        } catch (fallbackError) {
          logger.error(
            `Error sending welcome message to fallback text channel ${textChannel.id}: ${fallbackError.message}`
          );
        }
      }
    }
  } catch (error) {
    logger.error(`Error in sendWelcomeMessage for channel ${channelId}: ${error.message}`);

    if (guildWelcomeCooldowns.has(guildId)) {
      guildWelcomeCooldowns.delete(guildId);
      logger.warn(`Removed guild ${guildId} from cooldown due to error in sendWelcomeMessage.`);
    }
  }
}

/**
 * Creates a modal for the user to input the number of messages to purge
 *
 * @param {string} channelId - The channel ID where the purge will happen
 * @returns {ModalBuilder} The modal for purge input
 */
function createPurgeModal(channelId: string): ModalBuilder {
  const modal = new ModalBuilder()
    .setCustomId(`purge_modal_${channelId}`)
    .setTitle('Purge Messages');

  const purgeAmountInput = new TextInputBuilder()
    .setCustomId('purge_amount')
    .setLabel('Number of messages to delete (0-99)')
    .setPlaceholder('Enter a number between 0 and 99')
    .setMinLength(1)
    .setMaxLength(2)
    .setRequired(true)
    .setStyle(TextInputStyle.Short);

  const actionRow = new ActionRowBuilder<TextInputBuilder>().addComponents(purgeAmountInput);
  modal.addComponents(actionRow);

  return modal;
}

/**
 * Handles the purge button click event
 *
 * @param {any} interaction - The interaction object
 * @param {ExtendedClient} client - The Discord client
 */
export async function handlePurgeButton(interaction: any, client: ExtendedClient) {
  const purgeButtonSource = `channelWelcome.handlePurgeButton:button:${interaction.customId}`;
  try {
    const customId = interaction.customId;
    const channelId = customId.split('_').slice(2).join('_');

    if (!channelId) {
      logger.error(`${purgeButtonSource}: Could not extract channelId from customId ${customId}`);
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} Could not identify the target channel.`,
      });
      return;
    }

    let channel: GuildChannel | null = null;
    if (interaction.channel?.id === channelId) {
      channel = interaction.channel as GuildChannel;
    } else {
      channel = await interaction.guild?.channels.fetch(channelId).catch(() => null);
    }

    if (!channel) {
      logger.warn(`${purgeButtonSource}: Target channel ${channelId} no longer exists.`);
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} The associated channel no longer exists.`,
      });

      return;
    }

    if (!channel.isTextBased()) {
      logger.warn(
        `${purgeButtonSource}: Target channel ${channelId} (${channel.name}) is not text-based. Type: ${channel.type}`
      );
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} Cannot purge messages in this type of channel.`,
      });
      return;
    }

    const ownerId =
      client.tempChannels instanceof Map ? client.tempChannels.get(channelId) : undefined;

    if (!ownerId) {
      logger.warn(
        `${purgeButtonSource}: Channel ${channelId} is not registered as a temporary channel.`
      );
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} This is not a temporary channel managed by the bot.`,
      });
      return;
    }

    if (ownerId !== interaction.user.id) {
      logger.warn(
        `${purgeButtonSource}: User ${interaction.user.tag} tried to purge channel ${channelId} owned by ${ownerId}.`
      );
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} Only the channel owner (<@${ownerId}>) can purge messages.`,
      });
      return;
    }

    const modal = createPurgeModal(channel.id);
    await interaction.showModal(modal);
    logger.debug(
      `${purgeButtonSource}: Showed purge modal to user ${interaction.user.tag} for channel ${channel.name} (${channel.id})`
    );
  } catch (error) {
    await handleInteractionError('purgeButton', error, interaction, client);
  }
}

/**
 * Handles the purge modal submission
 *
 * @param {any} interaction - The interaction object
 * @param {ExtendedClient} client - The Discord client
 */
export async function handlePurgeModalSubmit(interaction: any, client: ExtendedClient) {
  const purgeModalSource = `channelWelcome.handlePurgeModalSubmit:modal:${interaction.customId}`;
  try {
    const modalCustomId = interaction.customId;
    const channelId = modalCustomId.split('_').slice(2).join('_');

    if (!channelId) {
      logger.error(
        `${purgeModalSource}: Could not extract channelId from modal customId ${modalCustomId}`
      );
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} Could not identify the target channel for purge.`,
      });
      return;
    }

    const purgeAmount = interaction.fields.getTextInputValue('purge_amount');
    const amount = parseInt(purgeAmount);

    if (isNaN(amount) || amount < 0 || amount > 99) {
      logger.warn(
        `${purgeModalSource}: Invalid purge amount entered by ${interaction.user.tag}: ${purgeAmount}`
      );
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} Please enter a valid number between 0 and 99.`,
      });
      return;
    }

    const channel = interaction.channel;
    if (!channel || channel.id !== channelId) {
      logger.error(
        `${purgeModalSource}: Modal submitted in wrong channel or channel unavailable. Expected ${channelId}, got ${interaction.channel?.id}`
      );
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} The channel context seems incorrect or the channel no longer exists.`,
      });
      return;
    }

    const ownerId =
      client.tempChannels instanceof Map ? client.tempChannels.get(channelId) : undefined;
    if (ownerId !== interaction.user.id) {
      logger.warn(
        `${purgeModalSource}: User ${interaction.user.tag} submitted purge modal for channel ${channelId} owned by ${ownerId}. Denying.`
      );
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} Ownership mismatch. Only the channel owner can confirm the purge.`,
      });
      return;
    }

    if (amount === 0) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CHECK} No messages were deleted as you specified 0.`,
      });
      return;
    }

    try {
      const messages = await channel.messages.fetch({ limit: amount + 5 });

      const twoWeeksAgo = Date.now() - 14 * 24 * 60 * 60 * 1000;

      const filteredMessages = new Collection<string, Message>();

      messages.forEach(msg => {
        const isRecent = msg.createdTimestamp > twoWeeksAgo;

        const isWelcomeMessage =
          msg.author.id === client.user?.id &&
          msg.embeds.length > 0 &&
          msg.embeds[0].title?.includes('Voice Channel Created') &&
          msg.components.length > 0 &&
          msg.components[0]?.components[0]?.customId?.startsWith('purge_button_');

        if (isRecent && !isWelcomeMessage) {
          filteredMessages.set(msg.id, msg);
        }
      });

      const messagesToDelete = filteredMessages.first(amount);
      const messagesToDeleteCollection = new Collection<string, Message>(
        messagesToDelete.map(m => [m.id, m])
      );

      let deletedCount = 0;

      if (messagesToDeleteCollection.size > 0) {
        if (messagesToDeleteCollection.size === 1) {
          try {
            await messagesToDeleteCollection.first()!.delete();
            deletedCount = 1;
          } catch (err) {
            logger.error(
              `${purgeModalSource}: Failed to delete single message ${messagesToDeleteCollection.first()!.id} in ${channel.id}: ${err.message}`
            );
          }
        } else {
          try {
            const result = await channel.bulkDelete(messagesToDeleteCollection, true);
            deletedCount = result.size;
            if (deletedCount < messagesToDeleteCollection.size) {
              logger.warn(
                `${purgeModalSource}: Bulk delete in ${channel.id} deleted ${deletedCount}/${messagesToDeleteCollection.size} messages. Some might have been too old or failed.`
              );
            }
          } catch (bulkError) {
            logger.error(
              `${purgeModalSource}: Bulk delete failed in ${channel.id}. Error: ${bulkError.message}. Falling back to individual deletion.`
            );

            deletedCount = 0;
            for (const message of messagesToDeleteCollection.values()) {
              try {
                await message.delete();
                deletedCount++;
              } catch (individualError) {
                logger.error(
                  `${purgeModalSource}: Failed to delete message ${message.id} individually in ${channel.id}: ${individualError.message}`
                );
              }
            }
          }
        }
      }

      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CHECK} Successfully purged ${deletedCount} message(s).`,
      });

      logger.info(
        `${purgeModalSource}: User ${interaction.user.tag} purged ${deletedCount} messages in channel ${channel.name} (${channel.id})`
      );
    } catch (error) {
      logger.error(
        `${purgeModalSource}: Error during message fetching/purging in ${channel.id}: ${error.message}`
      );
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].CROSS} An error occurred while purging messages. Please check my permissions or try again.`,
      });
    }
  } catch (error) {
    await handleInteractionError('purgeModalSubmit', error, interaction, client);
  }
}
