import { ActionRowBuilder, StringSelectMenuBuilder } from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'unblock';

export const execute = async (interaction, client, userChannel) => {
  try {
    const ownerId = interaction.user.id;
    const settings = (await dataManager.getUserSettings(ownerId)) || {};
    const blockedUserIds = settings.blockedUsers || [];

    if (blockedUserIds.length === 0) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} You have no users currently blocked.`,
      });
      return;
    }

    const validBlockedUsers = [];
    for (const userId of blockedUserIds) {
      try {
        if (userId === client.user.id) continue;

        const user = await client.users.fetch(userId);
        validBlockedUsers.push({
          id: userId,
          tag: user.tag,
          username: user.username,
        });
      } catch {
        logger.warn(`Failed to fetch user ${userId} for unblock menu, removing from settings.`);

        settings.blockedUsers = settings.blockedUsers.filter(id => id !== userId);
      }
    }

    if (validBlockedUsers.length === 0) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} No valid blocked users found to unblock.`,
      });

      return;
    }

    const unblockSelect = new StringSelectMenuBuilder()
      .setCustomId(`unblock_select_${userChannel.id}`)
      .setPlaceholder('Select user(s) to unblock')
      .setMinValues(1)
      .setMaxValues(validBlockedUsers.length > 10 ? 10 : validBlockedUsers.length);

    for (const user of validBlockedUsers) {
      unblockSelect.addOptions({
        label: user.tag,
        description: `User ID: ${user.id}`,
        value: user.id,
      });
    }

    const row: any = new ActionRowBuilder().addComponents(unblockSelect);

    await replyOrFollowUpEphemeral(interaction, {
      content:
        'Select user(s) to unblock. They will regain access based on channel privacy settings:',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending unblock select menu: ${error.message}`);
    await handleInteractionError('unblock.execute', error, interaction, client);
  }
};

export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `unblock.handleSelectMenu:selectMenu:${interaction.customId}`;
  if (!interaction.isStringSelectMenu()) return;

  try {
    const selectedUserIds = interaction.values;
    const ownerId = interaction.user.id;
    const unblockedUserTags: string[] = [];
    let errorsOccurred = false;

    logger.debug(
      `${menuSource}: User ${ownerId} selected ${selectedUserIds.length} user(s) to unblock from channel ${targetChannel.id}`
    );

    const settings = (await dataManager.getUserSettings(ownerId)) || {};
    settings.blockedUsers = settings.blockedUsers || [];

    for (const selectedUserId of selectedUserIds) {
      if (!settings.blockedUsers.includes(selectedUserId)) {
        logger.warn(
          `${menuSource}: User ${selectedUserId} selected for unblock, but not found in settings for owner ${ownerId}.`
        );
        continue;
      }

      try {
        const selectedUser = await client.users.fetch(selectedUserId).catch(() => null);

        settings.blockedUsers = settings.blockedUsers.filter(id => id !== selectedUserId);
        logger.debug(`Removed ${selectedUserId} from blocked list for owner ${ownerId}`);

        let isInGuild = false;
        try {
          const guildMember = await targetChannel.guild.members
            .fetch(selectedUserId)
            .catch(() => null);
          isInGuild = !!guildMember;
        } catch (error) {
          logger.debug(
            `${menuSource}: Could not determine if user ${selectedUserId} is in guild: ${error.message}`
          );
        }

        if (isInGuild) {
          const overwrite = targetChannel.permissionOverwrites.cache.get(selectedUserId);
          if (overwrite) {
            await overwrite.delete(`Unblocked by channel owner ${interaction.user.tag}`);
            logger.debug(`${menuSource}: Deleted permission overwrite for user ${selectedUserId}`);
          } else {
            logger.debug(
              `${menuSource}: No permission overwrite found for user ${selectedUserId} in channel ${targetChannel.id}, but user is in guild.`
            );
          }
        } else {
          logger.debug(
            `${menuSource}: User ${selectedUserId} is not in guild, skipping permission overwrite deletion.`
          );
        }

        unblockedUserTags.push(selectedUser ? selectedUser.tag : selectedUserId);
      } catch (userUnblockError) {
        logger.error(
          `${menuSource}: Failed to unblock user ${selectedUserId}: ${userUnblockError.message}`
        );
        errorsOccurred = true;
      }
    }

    try {
      const settingsObj = { [ownerId]: settings };
      await dataManager.saveUserSettings(settingsObj);
      logger.debug(`Updated blocked users settings for owner ${ownerId} in MongoDB after unblock`);
    } catch (error) {
      logger.error(`Failed to save updated blocked users to MongoDB: ${error.message}`);

      global.saveUserSettings(ownerId);
      logger.debug(`Falling back to queue-based saving for unblock operation`);
    }

    let replyMessage = '';
    if (unblockedUserTags.length > 0) {
      replyMessage += `${EMOJI[client.user.id].CHECK} Unblocked ${unblockedUserTags.join(', ')}.`;
    }
    if (errorsOccurred) {
      replyMessage +=
        (replyMessage ? '\n' : '') +
        '⚠️ Errors occurred while unblocking some users. Check bot permissions.';
    }
    if (!replyMessage) {
      replyMessage =
        'No users were unblocked (possibly due to errors or selecting non-blocked users).';
    }

    await replyOrFollowUpEphemeral(interaction, {
      content: replyMessage,
      components: [],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle unblock select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};
