/**
 * Data Manager - Handles persistent storage for the MyVC Bot
 * MongoDB-based data management system
 */
import { EventEmitter } from 'events';
import mongoose from 'mongoose';
import MongoConnection from '../data/connection';
import {
  blacklistedServerRepository,
  channelRepository,
  guildRepository,
  tempChannelRepository,
  userRepository,
} from '../data/index';
import GuildSettingsModel, { DEFAULT_GUILD_SETTINGS } from '../data/models/guild';
import logger from './logger';

interface CacheEntry {
  data: any;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

interface PendingOperation {
  func: Function;
  args: any[];
  timestamp: number;
  retryCount: number;
}

class DataManager extends EventEmitter {
  private autoSaveInterval: number;
  private maxRetries: number;
  private retryDelay: number;
  private dirtyFlags: Set<string>;
  private autoSaveTimer: NodeJS.Timeout | null;
  private validationInterval: NodeJS.Timeout | null;
  private cacheCleanupTimer: NodeJS.Timeout | null;
  private mongoEnabled: boolean = false;
  private connectionAttemptInProgress: boolean = false;
  private connectionReady: boolean = false;
  private pendingOperations: Map<string, PendingOperation[]> = new Map();
  private cache: Map<string, CacheEntry> = new Map();
  private cacheTTL: number = 5 * 1000; // Reduced from 10s to 5s for memory optimization
  private maxCacheSize: number = 500; // Reduced from 1000 to 500 for memory optimization
  private readonly OPERATION_TIMEOUT = 30000;

  /**
   * Initialize the data manager
   * @param options - Configuration options
   */
  constructor(options: any = {}) {
    super();

    this.autoSaveInterval = options.autoSaveInterval || 1 * 1000;

    this.maxRetries = options.maxRetries || 2;
    this.retryDelay = options.retryDelay || 3000;

    this.dirtyFlags = new Set();

    this.autoSaveTimer = null;
    this.validationInterval = null;
    this.cacheCleanupTimer = null;

    this.startPeriodicValidation();
    this.startCacheCleanup();
  }

  /**
   * Check if the MongoDB connection is established and ready.
   */
  get isReady(): boolean {
    return this.mongoEnabled && this.connectionReady;
  }

  /**
   * Connect to MongoDB and return a Promise that resolves on success or rejects on initial failure.
   * Handles retries internally but resolves/rejects the returned promise based on the first attempt's outcome or eventual success.
   */
  public connectToMongoDB(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      if (this.isReady) {
        logger.debug('MongoDB already connected.');
        resolve();
        return;
      }

      if (this.connectionAttemptInProgress) {
        logger.debug('Connection attempt already in progress, waiting...');

        const waitInterval = setInterval(() => {
          if (this.isReady) {
            clearInterval(waitInterval);
            resolve();
          } else if (!this.connectionAttemptInProgress) {
            clearInterval(waitInterval);
            reject(new Error('Connection attempt failed.'));
          }
        }, 500);
        return;
      }

      logger.info('Attempting to connect to MongoDB...');
      this.connectionAttemptInProgress = true;
      this.connectionReady = false;

      try {
        await MongoConnection.getInstance().connect();
        this.mongoEnabled = true;
        this.connectionReady = true;
        logger.info('Connected to MongoDB successfully');

        this.startAutoSave();

        this.processPendingOperations();

        this.syncPendingGuildSettings();

        this.connectionAttemptInProgress = false;
        resolve();
      } catch (error) {
        this.mongoEnabled = false;
        this.connectionReady = false;
        logger.error('Failed to connect to MongoDB:', error);
        this.connectionAttemptInProgress = false;

        reject(error);

        logger.info('Scheduling background reconnection attempt in 5 seconds...');
        setTimeout(() => {
          if (!this.isReady) {
            this.connectToMongoDB().catch(() => {
              logger.warn('Background reconnection attempt also failed.');
            });
          }
        }, 5000);
      }
    });
  }

  /**
   * Sync any pending guild settings stored in memory during MongoDB downtime
   */
  private async syncPendingGuildSettings(): Promise<void> {
    try {
      let client = null;
      try {
        if (this.isReady) {
          const indexModule = require('../index');
          client = indexModule && indexModule.client;
        }
      } catch {
        logger.warn('Could not access client from index.ts, skipping guild settings sync');
        return;
      }

      if (client && client.pendingGuildSettings && client.pendingGuildSettings.size > 0) {
        logger.info(
          `Syncing ${client.pendingGuildSettings.size} pending guild settings to MongoDB`
        );

        for (const [guildId, settings] of client.pendingGuildSettings.entries()) {
          try {
            await this.forceSyncGuildSettings(
              guildId,
              settings.joinChannelId,
              settings.interfaceChannel,
              settings.categoryId
            );
            logger.info(`Successfully synced pending settings for guild ${guildId}`);

            client.pendingGuildSettings.delete(guildId);
          } catch (syncError) {
            logger.error(`Failed to sync pending settings for guild ${guildId}:`, syncError);

            settings.pendingSince = new Date();
            settings.lastSyncAttempt = new Date();
            settings.syncError = syncError.message;
          }
        }
      }
    } catch (error) {
      logger.error('Error syncing pending guild settings:', error);
    }
  }

  /**
   * Process any operations that were queued while disconnected
   */
  private async processPendingOperations(): Promise<void> {
    if (!this.shouldUseMongoDB()) {
      return;
    }

    for (const [type, operations] of this.pendingOperations.entries()) {
      logger.info(`Processing ${operations.length} pending operations for ${type}`);

      for (const operation of operations) {
        try {
          const operationAge = Date.now() - operation.timestamp;
          if (operationAge > 5 * 60 * 1000) {
            logger.warn(`Skipping stale operation for ${type} (age: ${operationAge}ms)`);
            continue;
          }

          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Operation timeout')), this.OPERATION_TIMEOUT);
          });

          await Promise.race([operation.func(...operation.args), timeoutPromise]);
        } catch (error) {
          operation.retryCount++;
          if (operation.retryCount < this.maxRetries) {
            logger.warn(
              `Retrying operation for ${type} (attempt ${operation.retryCount + 1}/${this.maxRetries}):`,
              error
            );

            continue;
          } else {
            logger.error(
              `Failed to process operation for ${type} after ${this.maxRetries} retries:`,
              error
            );
          }
        }
      }

      this.pendingOperations.delete(type);
    }
  }

  /**
   * Queue an operation for later execution when MongoDB becomes available
   */
  private queueOperation(type: string, func: Function, ...args: any[]): void {
    if (!this.pendingOperations.has(type)) {
      this.pendingOperations.set(type, []);
    }

    this.pendingOperations.get(type)?.push({
      func,
      args,
      timestamp: Date.now(),
      retryCount: 0,
    });

    logger.debug(`Queued ${type} operation for later execution`);
  }

  /**
   * Attempt an operation with retry logic
   */
  private async withRetry<T>(operation: () => Promise<T>, retries = this.maxRetries): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (retries <= 0) {
        throw error;
      }

      logger.warn(`Operation failed, retrying... (${retries} attempts left):`, error);
      await new Promise(resolve => setTimeout(resolve, this.retryDelay));
      return this.withRetry(operation, retries - 1);
    }
  }

  /**
   * Get cached data with access tracking
   */
  getCached<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > this.cacheTTL) {
      this.cache.delete(key);
      return null;
    }

    cached.accessCount++;
    cached.lastAccessed = now;

    return cached.data as T;
  }

  /**
   * Set cache data with access tracking
   */
  setCache<T>(key: string, data: T): void {
    if (this.cache.size >= this.maxCacheSize) {
      this.evictLeastRecentlyUsed();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now(),
    });
  }

  /**
   * Clear specific cache entry or all cache
   */
  clearCache(key?: string): void {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Evict least recently used cache entries
   */
  private evictLeastRecentlyUsed(): void {
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

    const toRemove = Math.max(1, Math.floor(entries.length * 0.1));
    for (let i = 0; i < toRemove; i++) {
      this.cache.delete(entries[i][0]);
    }

    logger.debug(`Evicted ${toRemove} cache entries due to size limit`);
  }

  /**
   * Start cache cleanup interval
   */
  private startCacheCleanup(): void {
    this.cacheCleanupTimer = setInterval(
      () => {
        this.cleanupExpiredCache();
      },
      5 * 60 * 1000
    );

    logger.debug('Cache cleanup interval started');
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();
    let removedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.cacheTTL) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      logger.debug(`Cleaned up ${removedCount} expired cache entries`);
    }
  }

  /**
   * Stop cache cleanup interval
   */
  private stopCacheCleanup(): void {
    if (this.cacheCleanupTimer) {
      clearInterval(this.cacheCleanupTimer);
      this.cacheCleanupTimer = null;
      logger.debug('Cache cleanup interval stopped');
    }
  }

  /**
   * Check if MongoDB is connected and should be used
   * Uses the new isReady getter.
   */
  shouldUseMongoDB(): boolean {
    return this.isReady;
  }

  /**
   * Save data generically
   */
  async saveData(key: string, data: any): Promise<boolean> {
    try {
      if (!this.shouldUseMongoDB()) {
        this.queueOperation('saveData', this.saveData.bind(this), key, data);
        return false;
      }

      const generalCollection = mongoose.connection.collection('general_data');
      await generalCollection.updateOne(
        { key },
        { $set: { key, data, updatedAt: new Date() } },
        { upsert: true }
      );

      this.setCache(key, data);
      return true;
    } catch (error) {
      logger.error(`Error saving data for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Load data generically
   */
  async loadData(key: string): Promise<any | null> {
    const cached = this.getCached(key);
    if (cached) return cached;

    try {
      if (!this.shouldUseMongoDB()) {
        return null;
      }

      const generalCollection = mongoose.connection.collection('general_data');
      const result = await generalCollection.findOne({ key });

      if (result) {
        this.setCache(key, result.data);
        return result.data;
      }

      return null;
    } catch (error) {
      logger.error(`Error loading data for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Start auto-save interval
   */
  startAutoSave() {
    this.autoSaveTimer = setInterval(() => {
      this.saveAllData();
    }, this.autoSaveInterval);

    logger.debug(`Auto-save started with interval of ${this.autoSaveInterval / 1000} seconds`);
  }

  /**
   * Stop auto-save interval
   */
  stopAutoSave() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
      logger.debug('Auto-save stopped');
    }
  }

  /**
   * Start periodic validation of data
   */
  startPeriodicValidation() {
    const VALIDATION_INTERVAL = 5 * 60 * 1000;

    this.validationInterval = setInterval(() => {
      this.validateData();
    }, VALIDATION_INTERVAL);

    logger.debug(`Validation interval started (every ${VALIDATION_INTERVAL / 60000} minutes)`);
  }

  /**
   * Stop validation interval
   */
  stopValidation() {
    if (this.validationInterval) {
      clearInterval(this.validationInterval);
      this.validationInterval = null;
      logger.debug('Validation interval stopped');
    }
  }

  /**
   * Save all MongoDB data
   */
  async saveAllData() {
    if (!this.shouldUseMongoDB()) {
      logger.error('Cannot save data: MongoDB is not available');
      return false;
    }

    try {
      if (this.dirtyFlags.size > 0) {
        logger.info(`Saving ${this.dirtyFlags.size} dirty data types`);
        this.dirtyFlags.clear();
      }

      logger.debug('MongoDB data auto-saved');
      return true;
    } catch (error) {
      logger.error('Error during MongoDB auto-save:', error);
      return false;
    }
  }

  /**
   * Validate data integrity
   */
  async validateData() {
    if (!this.shouldUseMongoDB()) {
      logger.error('Cannot validate data: MongoDB is not available');
      return;
    }

    try {
      const isConnected = mongoose.connection.readyState === 1;
      if (!isConnected) {
        logger.warn('MongoDB connection validation failed - attempting reconnection');
        await this.connectToMongoDB();
        return;
      }

      const cacheStats = {
        totalEntries: this.cache.size,
        expiredEntries: 0,
        accessCounts: 0,
      };

      const now = Date.now();
      for (const [, entry] of this.cache.entries()) {
        if (now - entry.timestamp > this.cacheTTL) {
          cacheStats.expiredEntries++;
        }
        cacheStats.accessCounts += entry.accessCount;
      }

      logger.debug(`Data validation completed - Cache stats: ${JSON.stringify(cacheStats)}`);
    } catch (error) {
      logger.error('Error during data validation:', error);
    }
  }

  /**
   * Mark data type as dirty (needs saving)
   */
  markDirty(dataType: string): void {
    this.dirtyFlags.add(dataType);
    logger.debug(`Marked ${dataType} as dirty`);
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): { size: number; hitRate: number; totalAccesses: number } {
    let totalAccesses = 0;
    for (const entry of this.cache.values()) {
      totalAccesses += entry.accessCount;
    }

    return {
      size: this.cache.size,
      hitRate: totalAccesses > 0 ? totalAccesses / (totalAccesses + this.cache.size) : 0,
      totalAccesses,
    };
  }

  /**
   * Batch operation wrapper with error handling and retries
   */
  private async batchOperation<T>(
    operations: Array<() => Promise<T>>,
    batchSize: number = 10
  ): Promise<Array<T | Error>> {
    const results: Array<T | Error> = [];

    for (let i = 0; i < operations.length; i += batchSize) {
      const batch = operations.slice(i, i + batchSize);
      const batchPromises = batch.map(async operation => {
        try {
          return await this.withRetry(operation);
        } catch (error) {
          return error instanceof Error ? error : new Error(String(error));
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);
      results.push(
        ...batchResults.map(result =>
          result.status === 'fulfilled' ? result.value : result.reason
        )
      );
    }

    return results;
  }

  /**
   * Health check for the data manager
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    mongodb: boolean;
    cache: { size: number; expired: number };
    pendingOperations: number;
  }> {
    const health: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      mongodb: boolean;
      cache: { size: number; expired: number };
      pendingOperations: number;
    } = {
      status: 'healthy',
      mongodb: this.shouldUseMongoDB(),
      cache: { size: this.cache.size, expired: 0 },
      pendingOperations: Array.from(this.pendingOperations.values()).reduce(
        (sum, ops) => sum + ops.length,
        0
      ),
    };

    const now = Date.now();
    for (const entry of this.cache.values()) {
      if (now - entry.timestamp > this.cacheTTL) {
        health.cache.expired++;
      }
    }

    if (!health.mongodb) {
      health.status = 'unhealthy';
    } else if (health.pendingOperations > 50 || health.cache.expired > health.cache.size * 0.5) {
      health.status = 'degraded';
    }

    return health;
  }

  /**
   * Load user settings from MongoDB
   * @returns {Promise<Object>} User settings object
   */
  async loadUserSettings() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      const users = await userRepository.find({});
      const result = {};

      users.forEach(user => {
        result[user.userId] = user.settings;
      });

      return result;
    } catch (error) {
      logger.error('Error loading user settings from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Get settings for a single user from MongoDB
   * @param {string} userId - The user ID
   * @returns {Promise<Object|null>} User settings or null if not found
   */
  async getUserSettings(userId: string): Promise<any> {
    if (!this.shouldUseMongoDB()) {
      logger.warn('Cannot get user settings: MongoDB is not available');
      return null;
    }

    try {
      const user = await userRepository.findByUserId(userId);
      return user ? user.settings : null;
    } catch (error) {
      logger.error(`Error getting user settings for ${userId} from MongoDB:`, error);
      return null;
    }
  }

  /**
   * Save user settings to MongoDB
   * @param {Object} settings - User settings to save
   * @returns {Promise<boolean>} Success status
   */
  async saveUserSettings(settings) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      for (const [userId, userSettings] of Object.entries(settings)) {
        await userRepository.upsert(
          { userId },
          {
            userId,
            settings: userSettings,
          }
        );
      }
      return true;
    } catch (error) {
      logger.error('Error saving user settings to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Load channel settings from MongoDB
   * @returns {Promise<Object>} Channel settings object
   */
  async loadChannelSettings() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      const channels = await channelRepository.find({});
      const result = {};

      channels.forEach(channel => {
        result[channel.channelId] = { ...channel.settings, guildId: channel.guildId };
      });

      return result;
    } catch (error) {
      logger.error('Error loading channel settings from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Save channel settings to MongoDB
   * @param {Object} settings - Channel settings to save
   * @returns {Promise<boolean>} Success status
   */
  async saveChannelSettings(settings) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      for (const [channelId, channelData] of Object.entries(settings)) {
        const data = channelData as any;
        const guildId = data.guildId || 'unknown';

        await channelRepository.upsert(
          { channelId },
          {
            channelId,
            guildId,
            settings: channelData,
          }
        );
      }
      return true;
    } catch (error) {
      logger.error('Error saving channel settings to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Load guild settings from MongoDB
   * @returns {Promise<Object>} Guild settings object
   */
  async loadGuildSettings() {
    const cached = this.getCached('guild_settings');
    if (cached) return cached;

    if (!this.shouldUseMongoDB()) {
      logger.warn('Cannot load guild settings: MongoDB is not available');
      this.queueOperation('loadGuildSettings', this.loadGuildSettings.bind(this));
      return {};
    }

    try {
      return await this.withRetry(async () => {
        const guilds = await guildRepository.find({});
        const result = {};

        guilds.forEach(guild => {
          result[guild.guildId] = {
            prefix: guild.prefix,
            tempChannelLimit: guild.tempChannelLimit,
            userChannelLimit: guild.userChannelLimit,
            defaultUserLimit: guild.defaultUserLimit,
            autoDeleteEmpty: guild.autoDeleteEmpty,
            autoDeleteDelay: guild.autoDeleteDelay,
            joinChannelId: guild.joinChannelId,
            tempCategoryId: guild.tempCategoryId,
            lastUpdated: guild.lastUpdated,
          };
        });

        this.setCache('guild_settings', result);
        return result;
      });
    } catch (error) {
      logger.error('Error loading guild settings from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Save guild settings to MongoDB
   * @param {Object} settings - Guild settings to save
   * @returns {Promise<boolean>} Success status
   */
  async saveGuildSettings(settings) {
    if (!this.shouldUseMongoDB()) {
      logger.warn('Cannot save guild settings: MongoDB is not available');
      this.queueOperation('saveGuildSettings', this.saveGuildSettings.bind(this), settings);
      return false;
    }

    try {
      return await this.withRetry(async () => {
        for (const [guildId, guildSettings] of Object.entries(settings)) {
          await guildRepository.upsert(
            { guildId },
            {
              guildId,
              ...(guildSettings as any),
              lastUpdated: new Date(),
            }
          );
        }

        this.setCache('guild_settings', settings);
        this.clearCache('guild_' + Object.keys(settings).join('_'));

        logger.info(`Successfully saved settings for ${Object.keys(settings).length} guilds`);
        return true;
      });
    } catch (error) {
      logger.error('Error saving guild settings to MongoDB:', error);
      return false;
    }
  }

  /**
   * Get per-guild settings, with fallback to defaults
   * @param {string} guildId - ID of the guild
   * @param {boolean} useCache - Whether to use cache (default: false)
   * @returns {Promise<Object>} Guild settings with defaults applied
   */
  async getGuildSettings(guildId: string, useCache: boolean = false): Promise<any> {
    if (useCache) {
      const cacheKey = `guild_${guildId}`;
      const cached = this.getCached(cacheKey);
      if (cached) {
        logger.debug(`Using cached settings for guild ${guildId}`);
        return cached;
      }
    }

    if (!this.shouldUseMongoDB()) {
      logger.warn(`Cannot get guild settings: MongoDB is not available for guild ${guildId}`);
      const defaultSettings = { guildId, ...DEFAULT_GUILD_SETTINGS, lastUpdated: new Date() };

      this.queueOperation('getGuildSettings', this.getGuildSettings.bind(this), guildId);

      return defaultSettings;
    }

    try {
      return await this.withRetry(async () => {
        logger.info(`Fetching settings from MongoDB for guild ${guildId}`);
        const settings = await guildRepository.findByGuildId(guildId, false);

        if (settings) {
          logger.info(
            `Found settings in MongoDB for guild ${guildId}: JoinChannel=${settings.joinChannelId}, TempCategory=${settings.tempCategoryId}`
          );

          const plainSettings = settings.toObject ? settings.toObject() : settings;
          const result = {
            ...DEFAULT_GUILD_SETTINGS,
            ...plainSettings,
            guildId,
          };

          const cacheKey = `guild_${guildId}`;
          this.setCache(cacheKey, result);

          return result;
        } else {
          logger.warn(
            `No settings found for guild ${guildId} in MongoDB. Creating default settings.`
          );

          const defaultSettings = { guildId, ...DEFAULT_GUILD_SETTINGS, lastUpdated: new Date() };
          await guildRepository.create(defaultSettings);

          if (useCache) {
            const cacheKey = `guild_${guildId}`;
            this.setCache(cacheKey, defaultSettings);
          }

          return defaultSettings;
        }
      });
    } catch (error) {
      logger.error(`Error retrieving guild settings for ${guildId} from MongoDB:`, error);

      const defaultSettings = { guildId, ...DEFAULT_GUILD_SETTINGS, lastUpdated: new Date() };
      return defaultSettings;
    }
  }

  /**
   * Update guild settings
   * @param {string} guildId - ID of the guild
   * @param {Object} settingsToUpdate - Settings to update
   * @returns {Promise<Object>} Updated guild settings
   */
  async updateGuildSettings(guildId: string, settingsToUpdate: any): Promise<any> {
    const cacheKey = `guild_${guildId}`;

    if (!this.shouldUseMongoDB()) {
      logger.warn(`Cannot update guild settings: MongoDB is not available for guild ${guildId}`);
      this.queueOperation(
        'updateGuildSettings',
        this.updateGuildSettings.bind(this),
        guildId,
        settingsToUpdate
      );

      const cached = this.getCached(cacheKey);
      if (cached) {
        const updated = {
          ...DEFAULT_GUILD_SETTINGS,
          guildId,
          ...settingsToUpdate,
          lastUpdated: new Date(),
        };

        this.setCache(cacheKey, updated);

        this.emit('guildSettingsChanged', guildId, updated);
        return updated;
      }

      const result = {
        ...DEFAULT_GUILD_SETTINGS,
        guildId,
        ...settingsToUpdate,
        lastUpdated: new Date(),
      };

      this.emit('guildSettingsChanged', guildId, result);
      return result;
    }

    try {
      const updateData = {
        ...DEFAULT_GUILD_SETTINGS,
        guildId,
        ...settingsToUpdate,
        lastUpdated: new Date(),
      };

      await GuildSettingsModel.replaceOne({ guildId }, updateData, { upsert: true });

      logger.info(`Successfully updated guild settings for guild ${guildId} in MongoDB`);

      this.clearCache(cacheKey);
      this.clearCache('guild_settings');

      const newSettings = await guildRepository.findByGuildId(guildId);

      let result;

      if (newSettings) {
        const plainObj = newSettings.toObject ? newSettings.toObject() : newSettings;
        result = {
          ...DEFAULT_GUILD_SETTINGS,
          ...plainObj,
        };
      } else {
        result = {
          ...DEFAULT_GUILD_SETTINGS,
          guildId,
          ...settingsToUpdate,
          lastUpdated: new Date(),
        };
      }

      this.setCache(cacheKey, result);

      this.emit('guildSettingsChanged', guildId, result);

      return result;
    } catch (error) {
      logger.error(`Error updating guild settings for ${guildId} in MongoDB:`, error);

      const result = {
        ...DEFAULT_GUILD_SETTINGS,
        guildId,
        ...settingsToUpdate,
        lastUpdated: new Date(),
      };

      return result;
    }
  }

  /**
   * Load blacklisted servers from MongoDB
   * @returns {Promise<Object>} Blacklisted servers object
   */
  async loadBlacklistedServers() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      const blacklistedObj = { blacklistedServers: {} };
      const blacklisted = await blacklistedServerRepository.find({});

      blacklisted.forEach(server => {
        blacklistedObj.blacklistedServers[server.guildId] = {
          reason: server.reason,
          blacklistedBy: server.blacklistedBy,
          blacklistedAt: server.blacklistedAt,
        };
      });

      return blacklistedObj;
    } catch (error) {
      logger.error('Error loading blacklisted servers from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Save blacklisted servers to MongoDB
   * @param {Object} data - Blacklisted servers data to save
   * @returns {Promise<boolean>} Success status
   */
  async saveBlacklistedServers(data) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      if (data && data.blacklistedServers) {
        for (const [guildId, blacklistData] of Object.entries(data.blacklistedServers)) {
          const typedData = blacklistData as any;
          await blacklistedServerRepository.upsert(
            { guildId },
            {
              guildId,
              reason: typedData.reason || 'No reason provided',
              blacklistedBy: typedData.blacklistedBy || 'unknown',
              blacklistedAt: typedData.blacklistedAt
                ? new Date(typedData.blacklistedAt)
                : new Date(),
            }
          );
        }
      }
      return true;
    } catch (error) {
      logger.error('Error saving blacklisted servers to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Load temporary channels from MongoDB
   * @returns {Promise<Object>} Temporary channels object mapping channel IDs to owner user IDs
   */
  async loadTempChannels() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      return await tempChannelRepository.getAllTempChannels();
    } catch (error) {
      logger.error('Error loading temp channels from MongoDB:', error);
      throw error;
    }
  }

  /**
   * Save temporary channels to MongoDB
   * @param {Map|Object} channels - Map or object of channel IDs to owner user IDs
   * @returns {Promise<boolean>} Success status
   */
  async saveTempChannels(channels) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    let channelsObj = channels;

    if (channels instanceof Map) {
      channelsObj = {};
      for (const [channelId, userId] of channels.entries()) {
        channelsObj[channelId] = userId;
      }
    }

    try {
      const currentChannelsInDB = await tempChannelRepository.getAllTempChannels();
      const currentChannelIds = Object.keys(currentChannelsInDB);
      const newChannelIds = Object.keys(channelsObj);

      const channelsToRemove = currentChannelIds.filter(id => !newChannelIds.includes(id));

      for (const channelId of channelsToRemove) {
        try {
          await tempChannelRepository.removeTempChannel(channelId);
          logger.debug(`Removed stale channel ${channelId} from database`);
        } catch (error) {
          logger.warn(`Failed to remove stale channel ${channelId} from database:`, error);
        }
      }

      for (const [channelId, ownerId] of Object.entries(channelsObj)) {
        try {
          const client = require('../index').client;
          const channel = client?.channels?.cache?.get(channelId);
          const guildId = channel?.guild?.id || 'unknown';

          await tempChannelRepository.upsert(
            { channelId },
            {
              channelId,
              ownerId: ownerId as string,
              guildId: guildId,
              createdAt: new Date(),
            }
          );
        } catch {
          logger.warn(`Could not determine guild ID for channel ${channelId}, using 'unknown'`);
          await tempChannelRepository.upsert(
            { channelId },
            {
              channelId,
              ownerId: ownerId as string,
              guildId: 'unknown',
              createdAt: new Date(),
            }
          );
        }
      }

      if (channelsToRemove.length > 0) {
        logger.info(`Removed ${channelsToRemove.length} stale channels from database`);
      }
      logger.info(`Saved ${Object.keys(channelsObj).length} temporary channels to MongoDB`);
      return true;
    } catch (error) {
      logger.error('Error saving temp channels to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Clean up stale temporary channels from database
   * Removes channels that no longer exist in Discord
   * @returns {Promise<number>} Number of channels removed
   */
  async cleanupStaleTempChannels() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
      return 0;
    }

    try {
      const client = require('../index').client;
      if (!client || !client.guilds) {
        logger.warn('Discord client not available for stale channel cleanup');
        return 0;
      }

      const allChannels = await tempChannelRepository.getAllTempChannels();
      const channelIds = Object.keys(allChannels);

      if (channelIds.length === 0) {
        logger.debug('No temporary channels in database to check');
        return 0;
      }

      let removedCount = 0;

      for (const channelId of channelIds) {
        let channelExists = false;

        for (const guild of client.guilds.cache.values()) {
          try {
            const channel = guild.channels.cache.get(channelId);
            if (channel) {
              channelExists = true;
              break;
            }
          } catch (error) {
            continue;
          }
        }

        if (!channelExists) {
          try {
            await tempChannelRepository.removeTempChannel(channelId);
            removedCount++;
            logger.debug(
              `Removed stale channel ${channelId} from database (doesn't exist in Discord)`
            );
          } catch (error) {
            logger.warn(`Failed to remove stale channel ${channelId} from database:`, error);
          }
        }
      }

      if (removedCount > 0) {
        logger.info(`Database cleanup: Removed ${removedCount} stale channels from database`);
      } else {
        logger.debug('Database cleanup: No stale channels found');
      }

      return removedCount;
    } catch (error) {
      logger.error('Error during stale channel cleanup:', error);
      return 0;
    }
  }

  /**
   * Clean up invalid guild settings (channels that no longer exist)
   * @returns {Promise<number>} Number of guilds cleaned up
   */
  async cleanupInvalidGuildSettings() {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
      return 0;
    }

    try {
      const client = require('../index').client;
      if (!client || !client.guilds) {
        logger.warn('Discord client not available for guild settings cleanup');
        return 0;
      }

      const allGuildSettings = await this.loadGuildSettings();
      let cleanedCount = 0;

      for (const [guildId, settings] of Object.entries(allGuildSettings)) {
        const guild = client.guilds.cache.get(guildId);
        if (!guild) {
          continue;
        }

        let needsUpdate = false;
        const updates: any = {};

        if (settings.joinChannelId) {
          try {
            const joinChannel = await guild.channels
              .fetch(settings.joinChannelId)
              .catch(() => null);
            if (!joinChannel) {
              logger.warn(
                `Join channel ${settings.joinChannelId} no longer exists in guild ${guildId}, removing from settings`
              );
              updates.joinChannelId = null;
              needsUpdate = true;
            }
          } catch (error) {
            if (error.message === 'Unknown Channel') {
              logger.warn(
                `Join channel ${settings.joinChannelId} no longer exists in guild ${guildId}, removing from settings`
              );
              updates.joinChannelId = null;
              needsUpdate = true;
            }
          }
        }

        if (settings.tempCategoryId) {
          try {
            const category = await guild.channels.fetch(settings.tempCategoryId).catch(() => null);
            if (!category) {
              logger.warn(
                `Temp category ${settings.tempCategoryId} no longer exists in guild ${guildId}, removing from settings`
              );
              updates.tempCategoryId = null;
              needsUpdate = true;
            }
          } catch (error) {
            if (error.message === 'Unknown Channel') {
              logger.warn(
                `Temp category ${settings.tempCategoryId} no longer exists in guild ${guildId}, removing from settings`
              );
              updates.tempCategoryId = null;
              needsUpdate = true;
            }
          }
        }

        if (needsUpdate) {
          try {
            await this.updateGuildSettings(guildId, updates);
            cleanedCount++;
            logger.info(`Cleaned up invalid settings for guild ${guildId}`);
          } catch (error) {
            logger.error(`Failed to clean up settings for guild ${guildId}:`, error);
          }
        }
      }

      if (cleanedCount > 0) {
        logger.info(
          `Guild settings cleanup: Fixed ${cleanedCount} guilds with invalid channel references`
        );
      } else {
        logger.debug('Guild settings cleanup: No invalid settings found');
      }

      return cleanedCount;
    } catch (error) {
      logger.error('Error during guild settings cleanup:', error);
      return 0;
    }
  }

  /**
   * Check if a server is blacklisted
   * @param {string} guildId - ID of the guild to check
   * @returns {Promise<Object|null>} Blacklist data or null if not blacklisted
   */
  async isServerBlacklisted(guildId) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      const blacklistedServer = await blacklistedServerRepository.findByGuildId(guildId);
      if (blacklistedServer) {
        return {
          reason: blacklistedServer.reason,
          blacklistedBy: blacklistedServer.blacklistedBy,
          blacklistedAt: blacklistedServer.blacklistedAt,
        };
      }
      return null;
    } catch (error) {
      logger.error(`Error checking if server ${guildId} is blacklisted in MongoDB:`, error);
      throw error;
    }
  }

  /**
   * Get a guild's prefix
   * @param {string} guildId - ID of the guild
   * @returns {Promise<string>} Guild prefix or default prefix
   */
  async getGuildPrefix(guildId) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      return await guildRepository.getGuildPrefix(guildId);
    } catch (error) {
      logger.error(`Error getting guild prefix for ${guildId} from MongoDB:`, error);

      const { DEFAULT_PREFIX } = require('../constants/devs');
      return DEFAULT_PREFIX;
    }
  }

  /**
   * Set a guild's prefix
   * @param {string} guildId - ID of the guild
   * @param {string} prefix - New prefix
   * @returns {Promise<boolean>} Success status
   */
  async setGuildPrefix(guildId, prefix) {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      await guildRepository.setGuildPrefix(guildId, prefix);

      this.clearCache(`guild_${guildId}`);
      this.clearCache('guild_settings');

      this.emit('guildPrefixChanged', guildId, prefix);

      return true;
    } catch (error) {
      logger.error(`Error setting guild prefix for ${guildId} in MongoDB:`, error);
      throw error;
    }
  }

  /**
   * Save bot performance statistics to MongoDB
   * @param {Object} stats - Performance statistics to save
   */
  async saveBotStats(stats: any): Promise<void> {
    if (!this.shouldUseMongoDB()) {
      logger.error('MongoDB is required for data operations');
    }

    try {
      const statsCollection = mongoose.connection.collection('bot_stats');
      await statsCollection.insertOne(stats);
      logger.info('Successfully saved bot stats to MongoDB');
    } catch (error) {
      logger.error('Failed to save bot stats to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Cleanup resources before shutdown
   */
  async cleanup() {
    try {
      this.stopAutoSave();
      this.stopValidation();
      this.stopCacheCleanup();

      logger.info(
        `Cleanup: Flushing ${this.cache.size} cached items and ${Array.from(this.pendingOperations.keys()).length} pending operations`
      );

      if (this.shouldUseMongoDB()) {
        await this.processPendingOperations();
      }

      const saveResult = await this.saveAllData();
      logger.info(`Final data save ${saveResult ? 'succeeded' : 'failed'}`);

      try {
        await MongoConnection.getInstance().disconnect();
        logger.info('MongoDB connection closed successfully');
      } catch (dbError) {
        logger.error('Error closing MongoDB connection:', dbError);
      }

      this.clearCache();
      logger.info('All caches cleared');

      logger.info('Data manager cleanup completed');
      return true;
    } catch (error) {
      logger.error('Error during data manager cleanup:', error);
      return false;
    }
  }

  /**
   * Force synchronize guild settings with the database
   * Used as a fallback when normal update methods fail
   * @param guildId - The guild ID
   * @param joinChannelId - The join channel ID
   * @param categoryId - The category ID
   */
  async forceSyncGuildSettings(
    guildId: string,
    joinChannelId: string,
    interfaceChannel: string,
    categoryId: string
  ): Promise<boolean> {
    try {
      logger.info(
        `Force syncing guild settings for ${guildId} with Join=${joinChannelId}, Category=${categoryId}`
      );

      this.clearCache(`guild_${guildId}`);
      this.clearCache('guild_settings');

      const settings = {
        ...DEFAULT_GUILD_SETTINGS,
        guildId,
        joinChannelId,
        interfaceChannel,
        tempCategoryId: categoryId,
        lastUpdated: new Date(),
      };

      await GuildSettingsModel.deleteOne({ guildId });
      await GuildSettingsModel.create(settings);

      const collection = mongoose.connection.collection('guildsettings');
      const result = await collection.findOne({ guildId });

      console.log(settings);

      if (result) {
        logger.info(
          `Force sync successful - verified in database: ${JSON.stringify({
            joinChannelId: result.joinChannelId,
            tempCategoryId: result.tempCategoryId,
          })}`
        );

        const cacheKey = `guild_${guildId}`;
        this.setCache(cacheKey, settings);

        const allSettings = await this.loadGuildSettings();
        if (allSettings && typeof allSettings === 'object') {
          allSettings[guildId] = settings;
          this.setCache('guild_settings', allSettings);
        }

        this.emit('guildSettingsChanged', guildId, settings);

        return true;
      } else {
        logger.error(`Force sync failed - could not find document after update`);
        return false;
      }
    } catch (error) {
      logger.error(`Force sync failed with error: ${error.message}`);
      return false;
    }
  }

  /**
   * Load all guild prefixes into memory
   * Call this during bot startup to reduce database calls
   */
  async preloadGuildPrefixes() {
    if (!this.shouldUseMongoDB()) {
      logger.warn('Cannot preload guild prefixes: MongoDB is not available');
      return;
    }

    try {
      logger.info('Preloading guild prefixes...');
      const guilds = await guildRepository.find({});
      const { DEFAULT_PREFIX } = require('../constants/devs');

      let count = 0;
      for (const guild of guilds) {
        const prefix = guild.prefix || DEFAULT_PREFIX || DEFAULT_GUILD_SETTINGS.prefix;

        this.emit('guildPrefixChanged', guild.guildId, prefix);
        count++;
      }

      logger.info(`Successfully preloaded ${count} guild prefixes`);
    } catch (error) {
      logger.error('Error preloading guild prefixes:', error);
    }
  }
}

const dataManagerInstance = new DataManager();
export default dataManagerInstance;
