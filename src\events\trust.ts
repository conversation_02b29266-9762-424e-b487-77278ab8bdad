import { ActionRowBuilder, UserSelectMenuBuilder } from 'discord.js';
import dataManager from '../utils/dataManager';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'trust';

export const execute = async (interaction, client, userChannel) => {
  try {
    const trustSelect = new UserSelectMenuBuilder()
      .setCustomId(`trust_select_${userChannel.id}`)
      .setPlaceholder('Select user(s) to trust')
      .setMinValues(1)
      .setMaxValues(10);

    const row: any = new ActionRowBuilder().addComponents(trustSelect);

    await replyOrFollowUpEphemeral(interaction, {
      content:
        'Select user(s) to trust. They will be able to join even if the channel is locked/hidden:',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending trust select menu: ${error.message}`);
    await handleInteractionError('trust.execute', error, interaction, client);
  }
};

export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `trust.handleSelectMenu:selectMenu:${interaction.customId}`;

  if (!interaction.isUserSelectMenu()) return;

  try {
    const selectedUsers = interaction.users;
    const ownerId = interaction.user.id;
    const trustedUserTags: string[] = [];
    let errorsOccurred = false;

    logger.debug(
      `${menuSource}: User ${ownerId} selected ${selectedUsers.size} user(s) to trust for channel ${targetChannel.id}`
    );

    const settings = (await dataManager.getUserSettings(ownerId)) || { trustedUsers: [] };
    settings.trustedUsers = settings.trustedUsers || [];

    for (const [selectedUserId, selectedUser] of selectedUsers) {
      if (selectedUserId === ownerId) continue;
      if (selectedUser.bot) {
        logger.warn(`${menuSource}: User ${ownerId} attempted to trust bot ${selectedUser.tag}.`);
        continue;
      }

      try {
        if (!settings.trustedUsers.includes(selectedUserId)) {
          settings.trustedUsers.push(selectedUserId);
        }

        await targetChannel.permissionOverwrites.edit(
          selectedUserId,
          {
            ViewChannel: true,
            Connect: true,
            Speak: true,
          },
          `Trusted by channel owner ${interaction.user.tag}`
        );
        logger.debug(`Granted trust permissions to ${selectedUser.tag} (${selectedUserId})`);
        trustedUserTags.push(selectedUser.tag);
      } catch (userTrustError) {
        logger.error(
          `${menuSource}: Failed to trust user ${selectedUserId} (${selectedUser.tag}): ${userTrustError.message}`
        );
        errorsOccurred = true;
      }
    }

    logger.debug(`Updated trusted users settings for owner ${ownerId}`);

    let replyMessage = '';
    if (trustedUserTags.length > 0) {
      replyMessage += `${EMOJI[client.user.id].CHECK} Trusted ${trustedUserTags.join(', ')}.`;
    }
    if (errorsOccurred) {
      replyMessage +=
        (replyMessage ? '\n' : '') +
        '⚠️ Errors occurred while trusting some users. Check bot permissions.';
    }
    if (!replyMessage && selectedUsers.size > 0) {
      replyMessage = `${EMOJI[client.user.id].UNTRUST} No new users were trusted (you cannot trust yourself or bots).`;
    }
    if (!replyMessage) {
      replyMessage = 'No users were trusted.';
    }

    await replyOrFollowUpEphemeral(interaction, {
      content: replyMessage,
      components: [],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle trust select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};
