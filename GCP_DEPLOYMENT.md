# Google Cloud Platform Deployment Guide

Deploy your Discord bot to Google Cloud Platform using Cloud Run for a scalable, cost-effective solution.

## 🚀 **Quick Deploy**

### Prerequisites
- ✅ Google Cloud account
- ✅ Discord bot token
- ✅ MongoDB database (Atlas recommended)
- 🔧 Google Cloud CLI installed

## 📋 **Step 1: Setup Google Cloud CLI**

### Install gcloud CLI:
```bash
# Windows (PowerShell as Administrator)
(New-Object Net.WebClient).DownloadFile("https://dl.google.com/dl/cloudsdk/channels/rapid/GoogleCloudSDKInstaller.exe", "$env:Temp\GoogleCloudSDKInstaller.exe")
& $env:Temp\GoogleCloudSDKInstaller.exe

# Mac
brew install --cask google-cloud-sdk

# Linux
curl https://sdk.cloud.google.com | bash
exec -l $SHELL
```

### Authenticate and setup:
```bash
# Login to Google Cloud
gcloud auth login

# Set your project ID (replace with your actual project ID)
gcloud config set project YOUR_PROJECT_ID

# Enable required APIs
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

## 🔧 **Step 2: Configure Environment Variables**

Create a `.env` file with your configuration:
```env
TOKEN=your_discord_bot_token
CLIENT_ID=your_discord_client_id
MONGODB_URI=your_mongodb_connection_string
CONSOLE_LOG_LEVEL=INFO
NODE_ENV=production
PORT=8080
SHARD_COUNT=1
SHARDED=false
```

## 🐳 **Step 3: Deploy to Cloud Run**

### Option A: Direct Deploy (Recommended)
```bash
# Build and deploy in one command
gcloud run deploy discord-bot \
  --source . \
  --region us-central1 \
  --platform managed \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 1 \
  --max-instances 1 \
  --min-instances 1 \
  --port 8080 \
  --set-env-vars NODE_ENV=production,PORT=8080,SHARD_COUNT=1,SHARDED=false \
  --set-env-vars TOKEN=your_token_here,CLIENT_ID=your_client_id,MONGODB_URI=your_mongodb_uri
```

### Option B: Using Cloud Build
```bash
# Submit build to Cloud Build
gcloud builds submit --config cloudbuild.yaml

# The cloudbuild.yaml will automatically deploy to Cloud Run
```

### Option C: Manual Docker Build
```bash
# Build the Docker image
docker build -t gcr.io/YOUR_PROJECT_ID/discord-bot .

# Push to Google Container Registry
docker push gcr.io/YOUR_PROJECT_ID/discord-bot

# Deploy to Cloud Run
gcloud run deploy discord-bot \
  --image gcr.io/YOUR_PROJECT_ID/discord-bot \
  --region us-central1 \
  --platform managed \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 1 \
  --max-instances 1 \
  --min-instances 1
```

## ⚙️ **Step 4: Set Environment Variables**

### Using gcloud CLI:
```bash
# Set environment variables
gcloud run services update discord-bot \
  --region us-central1 \
  --set-env-vars TOKEN=your_token,CLIENT_ID=your_client_id,MONGODB_URI=your_mongodb_uri

# Or set them individually
gcloud run services update discord-bot --region us-central1 --set-env-vars TOKEN=your_token
gcloud run services update discord-bot --region us-central1 --set-env-vars CLIENT_ID=your_client_id
gcloud run services update discord-bot --region us-central1 --set-env-vars MONGODB_URI=your_mongodb_uri
```

### Using Google Cloud Console:
1. Go to [Cloud Run Console](https://console.cloud.google.com/run)
2. Click on your `discord-bot` service
3. Click "EDIT & DEPLOY NEW REVISION"
4. Go to "Variables & Secrets" tab
5. Add your environment variables

## 📊 **Step 5: Monitor and Manage**

### View logs:
```bash
# View recent logs
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=discord-bot" --limit 50

# Follow logs in real-time
gcloud logs tail "resource.type=cloud_run_revision AND resource.labels.service_name=discord-bot"
```

### Check service status:
```bash
# Get service details
gcloud run services describe discord-bot --region us-central1

# List all Cloud Run services
gcloud run services list
```

### Update the service:
```bash
# Deploy new version
gcloud run deploy discord-bot --source . --region us-central1

# Update environment variables
gcloud run services update discord-bot --region us-central1 --set-env-vars NEW_VAR=value
```

## 💰 **Cost Optimization**

### Cloud Run Pricing:
- **CPU**: $0.00002400 per vCPU-second
- **Memory**: $0.00000250 per GiB-second
- **Requests**: $0.40 per million requests
- **Free tier**: 2 million requests, 400,000 GiB-seconds, 200,000 vCPU-seconds per month

### Optimization tips:
1. **Use min-instances=1** to keep bot always running
2. **Set appropriate memory** (2Gi is usually sufficient)
3. **Monitor usage** through Cloud Console
4. **Use health checks** to ensure reliability

## 🔍 **Troubleshooting**

### Common issues:

**Build fails:**
```bash
# Check build logs
gcloud builds log BUILD_ID

# Verify Dockerfile syntax
docker build -t test-build .
```

**Service won't start:**
```bash
# Check service logs
gcloud logs read "resource.type=cloud_run_revision" --limit 20

# Verify environment variables
gcloud run services describe discord-bot --region us-central1
```

**Bot not responding:**
```bash
# Check if service is receiving traffic
gcloud run services get-iam-policy discord-bot --region us-central1

# Test health endpoint
curl https://your-service-url/health
```

## 🔗 **Useful Commands**

```bash
# Delete service
gcloud run services delete discord-bot --region us-central1

# Scale to zero (stop bot)
gcloud run services update discord-bot --region us-central1 --min-instances 0

# Scale back up
gcloud run services update discord-bot --region us-central1 --min-instances 1

# Get service URL
gcloud run services describe discord-bot --region us-central1 --format 'value(status.url)'
```

## 📞 **Support**

- [Google Cloud Run Documentation](https://cloud.google.com/run/docs)
- [Cloud Build Documentation](https://cloud.google.com/build/docs)
- [Discord.js Guide](https://discordjs.guide/)

Your Discord bot should now be running on Google Cloud Run! 🎉
