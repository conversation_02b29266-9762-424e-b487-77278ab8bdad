import { ActionRowBuilder, UserSelectMenuBuilder } from 'discord.js';
import logger from '../utils/logger';
import { replyOrFollowUpEphemeral, handleInteractionError } from '../utils/interactionUtils';
import { EMOJI } from '../constants/emoji';

export const name = 'kick';

export const execute = async (interaction, client, userChannel) => {
  try {
    const membersInChannel = userChannel.members
      .filter(member => member.id !== interaction.user.id)
      .map(member => ({ label: member.user.tag, value: member.id }));

    if (membersInChannel.length === 0) {
      await replyOrFollowUpEphemeral(interaction, {
        content: `${EMOJI[client.user.id].UNTRUST} No other users are currently in your channel to kick.`,
      });
      return;
    }

    const kickSelect = new UserSelectMenuBuilder()
      .setCustomId(`kick_select_${userChannel.id}`)
      .setPlaceholder('Select user(s) to kick from the channel')
      .setMinValues(1)
      .setMaxValues(membersInChannel.length > 10 ? 10 : membersInChannel.length);

    const row: any = new ActionRowBuilder().addComponents(kickSelect);

    await replyOrFollowUpEphemeral(interaction, {
      content: 'Select user(s) to kick from your current voice channel:',
      components: [row],
    });
  } catch (error) {
    logger.error(`Error sending kick select menu: ${error.message}`);
    await handleInteractionError('kick.execute', error, interaction, client);
  }
};

export const handleSelectMenu = async (interaction, client, targetChannel) => {
  const menuSource = `kick.handleSelectMenu:selectMenu:${interaction.customId}`;

  if (!interaction.isUserSelectMenu()) return;

  try {
    const selectedUsers = interaction.users;
    const ownerId = interaction.user.id;
    const kickedUserTags: string[] = [];
    let errorsOccurred = false;

    logger.debug(
      `${menuSource}: User ${ownerId} selected ${selectedUsers.size} user(s) to kick from channel ${targetChannel.id}`
    );

    for (const [selectedUserId, selectedUser] of selectedUsers) {
      if (selectedUserId === ownerId) continue;

      try {
        const member = await interaction.guild.members.fetch(selectedUserId).catch(() => null);
        if (!member) {
          logger.warn(`${menuSource}: Could not fetch member ${selectedUserId} to kick.`);
          errorsOccurred = true;
          continue;
        }

        if (member.voice.channelId !== targetChannel.id) {
          logger.warn(
            `${menuSource}: User ${selectedUser.tag} (${selectedUserId}) is no longer in channel ${targetChannel.id}.`
          );
          continue;
        }

        await member.voice.disconnect(`Kicked by channel owner ${interaction.user.tag}`);
        kickedUserTags.push(selectedUser.tag);
        logger.info(
          `Kicked user ${selectedUser.tag} (${selectedUserId}) from channel ${targetChannel.id} by owner ${ownerId}`
        );
      } catch (userKickError) {
        logger.error(
          `${menuSource}: Failed to kick user ${selectedUserId} (${selectedUser.tag}): ${userKickError.message}`
        );
        errorsOccurred = true;
      }
    }

    let replyMessage = '';
    if (kickedUserTags.length > 0) {
      replyMessage += `${EMOJI[client.user.id].CHECK} Kicked ${kickedUserTags.join(', ')}.`;
    }
    if (errorsOccurred) {
      replyMessage +=
        (replyMessage ? '\n' : '') +
        '⚠️ Errors occurred while kicking some users (they may have left or bot lacks permissions).';
    }
    if (!replyMessage) {
      replyMessage = 'No users were kicked (they might have left the channel already).';
    }

    await replyOrFollowUpEphemeral(interaction, {
      content: replyMessage,
      components: [],
    });
  } catch (error) {
    logger.error(`${menuSource}: Failed to handle kick select menu: ${error.message}`);
    await handleInteractionError(menuSource, error, interaction, client);
  }
};
